UPDATE order_import_temp
SET order_extend_id = order_no
WHERE order_extend_id IS NULL;

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, effect, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1925397143060398082, '000000', 5, 'LTL', 'LTL', 'shipping', '', 'default', 'light', 'N', '0', 103, 1, '2025-05-22 11:43:46', 1, '2025-05-22 11:43:52', '');

alter table order_import_temp
    add carton_label_oss_ids json null comment 'carton label oss表id' after shipping_label_file_name;

alter table order_import_temp
    add pallet_label_oss_ids json null comment 'oss表id' after carton_label_oss_ids;

alter table order_import_temp
    add item_label_oss_ids json null comment 'oss表id' after pallet_label_oss_ids;

alter table order_import_temp
    add other_oss_ids json null comment 'oss表id' after item_label_oss_ids;

alter table order_import_temp
    add bol_oss_id json null comment 'ossId' after warehouse_system_code;

alter table order_import_temp
    add order_extend_id varchar(256) null comment '行id';
