-- theo
INSERT INTO sys_dict_data(dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, effect, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1820393946459054082, '000000', 6, '测算异常', '6', 'order_exception_code', '', 'danger', 'light', 'N', '0', 103, 1, '2024-08-05 17:38:33', 1, '2024-08-05 17:38:33', '');
INSERT INTO sys_dict_data(dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, effect, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1820393830654320641, '000000', 5, '尾程异常', '5', 'order_exception_code', '', 'danger', 'light', 'N', '0', 103, 1, '2024-08-05 17:38:05', 1, '2024-08-05 17:38:05', '');
alter table order_import_temp
    add shipping_fee decimal(12, 4) null comment '尾程派送费' after shipping_label_oss_id;
alter table order_import_temp
    add shipping_total_amount decimal(12, 4) null comment '尾程派送费总计' after shipping_fee;

alter table orders
    modify exception_code int null comment '异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常 4:发货方式异常 5:尾程异常 6:测算异常';

alter table order_logistics_info
    add logistics_carrier_code varchar(255) null comment '承运商编号' after logistics_company_name;

alter table order_import_temp
    add logistics_carrier_code varchar(255) null comment '承运商编号' after logistics_company_name;
alter table order_import_record
    add order_temp_state tinyint(2) null comment '0 未下单 1正在下单 2 已下单  3 下单失败';


INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1827910760470597634, '000000', '承运商', 'shipping', '0', 103, 1, '2024-08-26 11:27:41', 1, '2024-08-26 11:27:41', '测算接口对应的指定承运商');
INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, effect, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1827911103346561025, '000000', 1, '任意', 'any', 'shipping', '', 'default', 'light', 'N', '0', 103, 1, '2024-08-26 11:29:03', 1, '2024-08-26 11:29:11', '');
INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, effect, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1827911053530812418, '000000', 3, 'FedEx', 'FedEx', 'shipping', '', 'default', 'light', 'N', '0', 103, 1, '2024-08-26 11:28:51', 1, '2024-08-26 11:29:25', '');
INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, effect, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1827910895854342145, '000000', 4, 'AMSP', 'AMSP', 'shipping', '', 'default', 'light', 'N', '0', 103, 1, '2024-08-26 11:28:13', 1, '2024-08-26 11:29:33', '');
INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, effect, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES (1827910851944173569, '000000', 2, 'UPS', 'UPS', 'shipping', '', 'default', 'light', 'N', '0', 103, 1, '2024-08-26 11:28:03', 1, '2024-08-26 11:29:17', '');
