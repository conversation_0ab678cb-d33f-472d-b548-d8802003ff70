create table order_item_tracking_import_record
(
    id                  bigint auto_increment primary key ,
    tenant_id           varchar(20)                        null comment '租户id',
    import_record_no    varchar(32)                        null comment '导入单号',
    import_file_name    varchar(255)                       null comment '导入名称',
    import_tracking_num int                                null comment '导入tracking数量',
    import_message      json                               null comment '错误信息',
    import_state        varchar(50)                        null comment '导入状态: Failed,Cancel,Pending,Importing,Success',
    del_flag            char     default 0                 null comment '删除标志(0代表存在 2代表删除)',
    create_by           bigint                             null comment '创建者',
    create_time         datetime default current_timestamp null comment '创建时间',
    update_by           bigint                             null comment '更新人',
    update_time         datetime                           null on update current_timestamp comment '更新时间'
);

alter table download_record
    add business_import_export_id bigint null comment '业务导入表主键' after id;

