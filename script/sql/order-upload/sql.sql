UPDATE order_import_temp
SET order_extend_id = order_no
WHERE order_extend_id IS NULL;

alter table order_import_temp
    add carton_label_oss_ids json null comment 'carton label oss表id' after shipping_label_file_name;

alter table order_import_temp
    add pallet_label_oss_ids json null comment 'oss表id' after carton_label_oss_ids;

alter table order_import_temp
    add item_label_oss_ids json null comment 'oss表id' after pallet_label_oss_ids;

alter table order_import_temp
    add other_oss_ids json null comment 'oss表id' after item_label_oss_ids;

alter table order_import_temp
    add bol_oss_id json null comment 'ossId' after warehouse_system_code;

alter table order_import_temp
    add order_extend_id varchar(256) null comment '行id';
