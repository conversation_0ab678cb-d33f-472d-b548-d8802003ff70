-- auto-generated definition
create table payment_approval
(
    id                    bigint auto_increment
        primary key,
    payment_no              varchar(255)                       not null comment '资金流水号',
    is_delivery             tinyint  default 0                 not null comment '投递是否成功 0失败 1成功',
    transaction_record_id   bigint                             not null comment 'transaction_record 主键',
    transaction_receipt_id  bigint                             not null comment 'transaction_receipt 主键',
    transaction_type        varchar(30)                        null comment '交易类型（Recharge-充值，Withdrawal-提现）',
    create_time             datetime default CURRENT_TIMESTAMP null,
    update_time             datetime default (now())           null on update CURRENT_TIMESTAMP,
    del_flag                tinyint  default 0                 not null comment '0代表存在 2代表删除',
    create_by               bigint                             null,
    update_by               bigint                             null
);

create index payment_no_index
    on payment_approval (payment_no);

create index payment_receipt_index
    on payment_approval (transaction_receipt_id);

create index payment_record_index
    on payment_approval (transaction_record_id);

