# http://editorconfig.org
# 告诉EditorConfig插件，这是根文件，不用继续往上查找
root = true

# 空格替代Tab缩进在各种编辑工具下效果一致
# 匹配全部文件
[*]
indent_style = space
indent_size = 4
# 设置字符集
charset = utf-8
# 结尾换行符，可选"lf"、"cr"、"crlf"
end_of_line = lf
# 删除一行中的前后空格
trim_trailing_whitespace = true
# 在文件结尾插入新行
insert_final_newline = true

[*.java]
max_line_length = 120
ij_continuation_indent_size = 4
ij_java_align_multiline_chained_methods = true
ij_java_align_multiline_parameters = true
ij_java_keep_simple_classes_in_one_line = true
ij_java_keep_simple_lambdas_in_one_line = true
ij_java_method_call_chain_wrap = normal
ij_java_method_parameters_wrap = normal

[*.{json,yml,yaml}]
indent_size = 2

[*.md]
insert_final_newline = false
trim_trailing_whitespace = false
