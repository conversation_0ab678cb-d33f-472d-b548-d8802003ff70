package com.zsmall.product.entity.domain.vo.productSku;

import com.zsmall.product.entity.domain.bo.product.ProductSitePriceBo;
import com.zsmall.product.entity.domain.vo.member.RuleLevelSkuPriceVo;
import lombok.Data;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;

/**
 * 响应体-商品SKU列表展示对象
 *
 * <AUTHOR>
 * @date 2023/6/6
 */
@Data
public class ProductSkuListVo {

    private Long productSkuId;
    private Long productId;

    private Long rulePriceId;
    private Long skuId;
    /**
     * 最小库存单位Sku
     */
    private String sku;

    /**
     * Sku唯一编号ItemNo.
     */
    private String productSkuCode;

    /**
     * 规格值名称
     */
    private String specValName;

    /**
     * 规格组合名称
     */
    private String specComposeName;

    /**
     * 库存数量
     */
    private Integer stockTotal;

    /**
     * 代发库存数量
     */
    private Integer proxyStockTotal;

    /**
     * SKU销售状态
     */
    private String skuShelfState;

    /**
     * 商品单价-站点改造后不关注
     */
    private String unitPrice;

    /**
     * 操作费-站点改造后不关注
     */
    private String operationFee;

    /**
     * 尾程派送费-站点改造后不关注
     */
    private String finalDeliveryFee;

    /**
     * 自提价-站点改造后不关注
     */
    private String pickUpPrice;

    /**
     * 代发价格-站点改造后不关注
     */
    private String dropShippingPrice;

    /**
     * 存在价格变更审核
     */
    private Boolean hasPriceChange;
    /**
     * 商品类型
     */
    private String productType;
    /**
     * 支持的物流
     */
    private String supportedLogistics;
    /**
     * 已售数量
     */
    private Integer stockSold = 0;

    /**
     * 站点价格组
     */
    @Setter
    private List<ProductSitePriceBo> sitePriceBos;

    /**
     * 会员站点价格
     */
    @Setter
    private List<ProductSitePriceBo> memberSitePriceBos;

    /**
     * todo 这里废弃挪到memberSitePriceBos内
     */
    @Setter
    private List<RuleLevelSkuPriceVo> skuPriceVoList;

    /**
     * 分仓库存集合
     */
    private List<HashMap<String,Object>> skuStockList;
}
