package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.bo.ProductSkuStockBo;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockListBo;
import com.zsmall.product.entity.domain.vo.ProductSkuStockVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.ProductSkuStockSimpleVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.SkuStockInfoVo;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品SKU库存Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuStockService extends ServiceImpl<ProductSkuStockMapper, ProductSkuStock> {

    private final ProductSkuStockMapper baseMapper;

    /**
     * 查询商品SKU库存
     */
    public ProductSkuStockVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    @InMethodLog(value = "查询商品SKU库存列表")
    public IPage<SkuStockInfoVo> queryPageList(StockListBo bo, Page<SkuStockInfoVo> page) {
        return baseMapper.queryPageList(bo, page);
    }

    @InMethodLog(value = "根据库存编号查询")
    public ProductSkuStock queryByStockCode(String stockCode) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuStock::getStockCode, stockCode);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 查询商品SKU库存列表
     */
    public List<ProductSkuStockVo> queryList(ProductSkuStockBo bo) {
        LambdaQueryWrapper<ProductSkuStock> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuStock> buildQueryWrapper(ProductSkuStockBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStockCode()), ProductSkuStock::getStockCode, bo.getStockCode());
        lqw.eq(bo.getStockTotal() != null, ProductSkuStock::getStockTotal, bo.getStockTotal());
        lqw.eq(bo.getStockReserved() != null, ProductSkuStock::getStockReserved, bo.getStockReserved());
        lqw.eq(bo.getStockAvailable() != null, ProductSkuStock::getStockAvailable, bo.getStockAvailable());
        lqw.eq(bo.getStockState() != null, ProductSkuStock::getStockState, bo.getStockState());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductSkuStock::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), ProductSkuStock::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), ProductSkuStock::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        return lqw;
    }

    /**
     * 新增商品SKU库存
     */
    public Boolean insertByBo(ProductSkuStockBo bo) {
        ProductSkuStock add = MapstructUtils.convert(bo, ProductSkuStock.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量新增商品SKU库存
     *
     * @param entityList
     * @return
     */
    public Boolean insertBatch(Collection<ProductSkuStock> entityList) {
        log.info("进入【批量新增商品SKU库存】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.insertBatch(entityList);
    }

    /**
     * 修改商品SKU库存
     */
    public Boolean updateByBo(ProductSkuStockBo bo) {
        ProductSkuStock update = MapstructUtils.convert(bo, ProductSkuStock.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuStock entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SKU库存
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("根据商品SKU编号获取Sku库存")
    public List<ProductSkuStockVo> queryByProductSkuCode(String productSkuCode) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuStock::getProductSkuCode, productSkuCode);
        return baseMapper.selectVoList(lqw);
    }

    @InMethodLog("根据商品SKU编号获取在售的Sku库存")
    public List<ProductSkuStockVo> queryValidByProductSkuCode(String productSkuCode) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuStock::getProductSkuCode, productSkuCode);
        lqw.eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据商品SKU唯一编号和仓库系统唯一编号获取有效的库存
     *
     * @param productSkuCode
     * @return
     */
    public ProductSkuStock queryByProductSkuCode(String productSkuCode, String warehouseSystemCode) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuStock::getProductSkuCode, productSkuCode);
        lqw.eq(ProductSkuStock::getWarehouseSystemCode, warehouseSystemCode);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw), TenantType.Manager, TenantType.Distributor);
    }

    /**
     * 根据商品SKU唯一编号和仓库系统唯一编号获取有效的库存
     *
     * @param productSkuCode
     * @return
     */
    public ProductSkuStock queryValidByProductSkuCodeNotTenant(String productSkuCode, String warehouseSystemCode) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuStock::getProductSkuCode, productSkuCode);
        lqw.eq(ProductSkuStock::getWarehouseSystemCode, warehouseSystemCode);
        lqw.eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw), TenantType.Manager, TenantType.Distributor);
    }

    @InMethodLog("根据商品SKU唯一编号和仓库系统唯一编号和库存标识获取有效的库存")
    public ProductSkuStock queryValidByProductSkuCodeAndDropShippingStockAvailableNotTenant(String productSkuCode, String warehouseSystemCode,Integer dropShippingStockAvailable) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuStock::getProductSkuCode, productSkuCode);
        lqw.eq(ProductSkuStock::getWarehouseSystemCode, warehouseSystemCode);
        lqw.eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid);
        lqw.eq(ProductSkuStock::getDropShippingStockAvailable, dropShippingStockAvailable);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw), TenantType.Manager, TenantType.Distributor);
    }

    /**
     * 根据SKU唯一编号查询简易视图对象
     *
     * @param productSkuCode
     * @return
     */
    public List<ProductSkuStockSimpleVo> querySimpleVoByProductSkuCode(String productSkuCode) {
        log.info("进入【根据SKU唯一编号查询简易视图对象】 productSkuCode = {}", productSkuCode);
        return baseMapper.querySimpleVoByProductSkuCode(productSkuCode);
    }

    /**
     * 统计在售库存总数（根据Sku唯一编号）
     *
     * @param productSkuCode
     * @return
     */
    public Integer sumStockTotal(String productSkuCode) {
        log.info("进入【统计在售库存总数（根据Sku唯一编号）】 productSkuCode = {}", productSkuCode);
        return baseMapper.sumStockTotal(productSkuCode);
    }

    /**
     * 根据商品SKU主键删除
     *
     * @param productSkuIdList
     * @return
     */
    public Boolean deleteByProductSkuIdList(List<Long> productSkuIdList) {
        log.info("进入【根据商品SKU主键删除】 productSkuIdList = {}", JSONUtil.toJsonStr(productSkuIdList));
        List<Long> ids = baseMapper.queryIdsByProductSkuIds(productSkuIdList);
        return this.deleteWithValidByIds(ids, false);
    }

    /**
     * 根据商品SKU唯一编号查询关联的库存主键集合
     *
     * @param productSkuCode
     * @return
     */
    public List<Long> queryIdsByProductSkuCode(String productSkuCode) {
        log.info("进入【根据商品SKU唯一编号查询关联的库存主键集合】 productSkuCode = {}", productSkuCode);
        return baseMapper.queryIdsByProductSkuCode(productSkuCode);
    }

    /**
     * 功能描述：
     * 根据提供的参数查询充足的库存
     *
     * @param destCountry           收货地国家缩写 例:us
     * @param productSkuCode
     * @param logisticsType         物流类型
     * @param adjustQuantity        扣减的库存数量
     * @param logisticsTemplateNo   物流模板编号
     * @param logisticsAccount      物流账号
     * @return {@link List }<{@link ProductSkuStock }>
     * <AUTHOR>
     * @date 2024/06/14
     */


    /**
     * 根据ID修改（无视租户）
     *
     * @param productSkuStock
     * @return
     */
    public Boolean updateByIdNotTenant(ProductSkuStock productSkuStock) {
        log.info("根据ID修改（无视租户） productSkuStock = {}", JSONUtil.toJsonStr(productSkuStock));
        return TenantHelper.ignore(() -> baseMapper.updateById(productSkuStock)) > 0;
    }

    /**
     * 根据商品SKU唯一编号和仓库编号更新库存
     *
     * @param productSkuCode
     * @param warehouseCode
     * @param stockQuantity
     * @return
     */
    public Boolean updateByProductSkuCodeAndWarehouse(String productSkuCode, String warehouseCode, Integer stockQuantity) {
        log.info("进入【根据商品SKU唯一编号和仓库编号更新库存】 productSkuCode = {}, warehouseCode = {}, stockQuantity = {}", productSkuCode, warehouseCode, stockQuantity);
        return baseMapper.updateByProductSkuCodeAndWarehouse(productSkuCode, warehouseCode, stockQuantity) > 0;
    }

    /**
     * 根据仓库系统编码更新商品库存
     * @param productSkuCode 商品编码
     * @param warehouseSysCode 仓库系统编码
     * @param stockQuantity 数量
     */
    public void updateByProductSkuCodeAndWarehouseV2(String productSkuCode, String warehouseSysCode, Integer stockQuantity,Integer dropShippingStockAvailable) {
        log.info("进入【根据商品SKU唯一编号和仓库系统编号更新库存】 productSkuCode = {}, warehouseSysCode = {}, stockQuantity = {}, dropShippingStockAvailable={}", productSkuCode, warehouseSysCode, stockQuantity,dropShippingStockAvailable);
         baseMapper.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouseSysCode, stockQuantity,dropShippingStockAvailable);
    }

    @InMethodLog("根据仓库和物流模板编号清除商品SKU库存的关联")
    public Boolean updateByWarehouseAndLogisticsTemplate(List<String> warehouseSystemCodeList, String logisticsTemplateNo) {
        LambdaUpdateWrapper<ProductSkuStock> luw = Wrappers.lambdaUpdate(ProductSkuStock.class);
        luw.set(ProductSkuStock::getLogisticsTemplateNo, null)
            .notIn(CollUtil.isNotEmpty(warehouseSystemCodeList), ProductSkuStock::getWarehouseSystemCode, warehouseSystemCodeList)
            .eq(ProductSkuStock::getLogisticsTemplateNo, logisticsTemplateNo);
        return baseMapper.update(null, luw) > 0;
    }

    @InMethodLog("根据物流模板编号查询关联库存")
    public List<ProductSkuStock> queryByLogisticsTemplateNo(String logisticsTemplateNo) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuStock::getLogisticsTemplateNo, logisticsTemplateNo);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    @InMethodLog("根据仓库系统编号查询有效的库存（且商品未删除的）")
    public Long countValidByWarehouseSystemCode(String warehouseSystemCode) {
        return baseMapper.countValidByWarehouseSystemCode(warehouseSystemCode);
    }
    @InMethodLog("根据提供的参数查询充足的库存")
    public List<ProductSkuStock> queryAdequateStockByParams(String destCountry, String productSkuCode, LogisticsTypeEnum logisticsType, Integer adjustQuantity, String logisticsTemplateNo, Boolean logisticsAccount) {
        return baseMapper.queryAdequateStockByParams(destCountry, productSkuCode, logisticsType.name(), adjustQuantity, logisticsTemplateNo, logisticsAccount);
    }
    @InMethodLog("根据商品code和满足扣减数量的条件查询充足的库存")
    public List<ProductSkuStock> queryAdequateInStock( String productSkuCode,
                                                      Integer adjustQuantity
                                                      ) {

        return baseMapper.queryAdequateInStock( productSkuCode, adjustQuantity);
    }

    @InMethodLog("根据商品code和代发库存和自提库存标识和满足扣减数量的条件查询充足的库存")
    public List<ProductSkuStock> queryAdequateInStockAndDropShippingStockAvailable( String productSkuCode,
                                                       Integer adjustQuantity,Integer dropShippingStockAvailable,String warehouseSystemCode
    ) {

        return baseMapper.queryAdequateInStockAndDropShippingStockAvailable( productSkuCode, adjustQuantity,dropShippingStockAvailable,warehouseSystemCode);
    }

    @InMethodLog("根据仓库编号查询库存数量")
    public Long countInventoryByWarehouseSystemCode(String tenantId,String warehouseSystemCode) {
        return baseMapper.sumInventoryByWarehouseSystemCode(tenantId,warehouseSystemCode);
    }

    @InMethodLog("根据商品code查询库存信息")
    public List<ProductSkuStock> listByProductSkuCodes(List<String> productSkuCodes) {
        LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSkuStock::getProductSkuCode, productSkuCodes);
        lqw.eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid);
        return baseMapper.selectList(lqw);
    }

    /**
     * 批量查询商品SKU库存简易视图
     * @param productSkuCodes SKU编号集合
     * @return SKU库存简易视图列表
     */
    public List<ProductSkuStockSimpleVo> querySimpleVoByProductSkuCodes(List<String> productSkuCodes) {
        log.info("进入【批量查询商品SKU库存简易视图】 productSkuCodes = {}", JSONUtil.toJsonStr(productSkuCodes));
        if (CollUtil.isEmpty(productSkuCodes)) {
            return new ArrayList<>();
        }

        // 使用真正的批量查询：一次IN查询获取所有SKU的库存信息
        List<ProductSkuStockSimpleVo> result = baseMapper.querySimpleVoByProductSkuCodes(productSkuCodes);
        log.info("【批量查询SKU库存】使用IN查询{}个SKU，获得{}条库存记录", productSkuCodes.size(), result.size());

        return result;
    }

    public void updateProductLockNum(Integer quantityTotal, String warehouseSystemCode, String productSku,
                                     String supportedLogistics,Boolean isAdd) {
        baseMapper.updateProductLockNum(quantityTotal,warehouseSystemCode,productSku,supportedLogistics,isAdd);
    }
}
