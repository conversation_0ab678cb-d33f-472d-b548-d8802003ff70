package com.zsmall.product.entity.domain.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 通用-Sku库存信息
 *
 * <AUTHOR>
 */
@Data
public class SkuStockInfoExportDTO {
    @ExcelProperty(value = "商品")
    private String productName;

    @ExcelProperty(value = "SKU")
    private String sku;

    @ExcelProperty(value = "Sku ID")
    private String productSkuCode;

    @ExcelProperty(value = "规格")
    private String specValName;

    @ExcelProperty(value = "仓库")
    private String warehouseName;

    @ExcelProperty(value = "仓库编码")
    private String warehouseSystemCode;

    @ExcelProperty(value = "库存（自提）")
    private Integer stockTotal;

    @ExcelProperty(value = "库存（一件代发）")
    private Integer proxyStockTotal;

    @ExcelProperty(value = "已售")
    private Integer sold;

//    @ExcelProperty(value = "自提价格")
//    private BigDecimal pickUpPrice;
//
//    @ExcelProperty(value = "代发价格")
//    private BigDecimal dropShippingPrice;

    @ExcelProperty(value = "Sku上/下架")
    private String stockStateString;
}
