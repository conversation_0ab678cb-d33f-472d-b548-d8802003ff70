package com.zsmall.product.entity.domain.bo.productSkuStock;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-查询库存列表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-查询库存列表")
public class StockListBo {

    @Schema(title = "查询类型")
    private String queryType;

    @Schema(title = "查询关键字")
    private String queryValue;

    @Schema(title = "排序方式：quantityAsc-数量升序，quantityDesc-数量降序，"
        + "updateDateTimeAsc-更新时间升序，updateDateTimeDesc-更新时间降序")
    private String sort;
    /**
     * 仓库编码
     */
    private String warehouseSystemCode;
    /**
     * 上下架状态
     */
    private String shelfState;
    private String tenantId;
}
