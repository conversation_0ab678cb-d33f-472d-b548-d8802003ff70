package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.TenantPayoneer;
import com.zsmall.system.entity.domain.bo.extraSetting.TenantPayoneerBo;
import com.zsmall.system.entity.domain.bo.extraSetting.TenantPayoneerDefaultBo;
import com.zsmall.system.entity.domain.bo.extraSetting.TenantPayoneerNameBo;
import com.zsmall.system.entity.domain.vo.extraSetting.TenantPayoneerVo;
import com.zsmall.system.entity.mapper.TenantPayoneerMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 支付账户管理Service接口
 *
 * <AUTHOR> Li
 * @date 2023-06-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ITenantPayoneerService extends ServiceImpl<TenantPayoneerMapper, TenantPayoneer> {

    /**
     * 查询支付账户管理
     */
    public TenantPayoneerVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询支付账户管理列表
     */
    public TableDataInfo<TenantPayoneerVo> queryPageList(TenantPayoneerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TenantPayoneer> lqw = buildQueryWrapper(bo);
        Page<TenantPayoneerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询支付账户管理列表
     */
    public List<TenantPayoneerVo> queryList(TenantPayoneerBo bo) {
        LambdaQueryWrapper<TenantPayoneer> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TenantPayoneer> buildQueryWrapper(TenantPayoneerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TenantPayoneer> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getAccountName()), TenantPayoneer::getAccountName, bo.getAccountName());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountId()), TenantPayoneer::getAccountId, bo.getAccountId());
        lqw.eq(bo.getIsDefault() != null, TenantPayoneer::getIsDefault, bo.getIsDefault());
        return lqw;
    }

    /**
     * 新增支付账户管理
     */
    public Boolean insertByBo(TenantPayoneerBo bo) {
        TenantPayoneer add = MapstructUtils.convert(bo, TenantPayoneer.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改支付账户管理
     */
    public Boolean updateByBo(TenantPayoneerBo bo) {
        TenantPayoneer update = MapstructUtils.convert(bo, TenantPayoneer.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TenantPayoneer entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除支付账户管理
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     *
     * @param bo
     * @return
     */
    public List<TenantPayoneerVo> getTenantPayoneerList(TenantPayoneerBo bo) {
        LambdaQueryWrapper<TenantPayoneer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantPayoneer::getDelFlag, "0").orderByDesc(TenantPayoneer::getIsDefault);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 不拼接租户 - 获取所有payoneer用户
     * @return
     */
    public List<TenantPayoneer> findAllPayoneers() {
        return TenantHelper.ignore(() -> baseMapper.selectList());
    }

    /**
     * 判断当前租户是否存在Payoneer
     * @param id
     * @return
     */
    public boolean isExistPayoneer(Long id) {
        LambdaQueryWrapper<TenantPayoneer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantPayoneer::getId, id);
        return baseMapper.exists(lqw);
    }

    /**
     * 判断是否存在Payoneer账户
     * @param accountId
     * @return
     */
    public boolean isExistPayoneerAccount(String accountId) {
        LambdaQueryWrapper<TenantPayoneer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantPayoneer::getAccountId, accountId);
        return baseMapper.exists(lqw);
    }

    /**
     * 判断是否存在Payoneer账户
     * @param accountId
     * @param tenantId
     * @return
     */
    public boolean isExistPayoneerAccount(String accountId, String tenantId) {
        LambdaQueryWrapper<TenantPayoneer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantPayoneer::getAccountId, accountId);
        lqw.eq(NoDeptTenantEntity::getTenantId, tenantId);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 判断是否存在默认的Payoneer账户
     * @param tenantId
     * @return
     */
    public boolean isExistDefaultTenantPayoneer(String tenantId) {
        LambdaQueryWrapper<TenantPayoneer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NoDeptTenantEntity::getTenantId, tenantId);
        lqw.eq(TenantPayoneer::getIsDefault, 1);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    public boolean updateByPayoneerNameBo(TenantPayoneerNameBo bo) {
        TenantPayoneer update = MapstructUtils.convert(bo, TenantPayoneer.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    public boolean updateByPayoneerDefaultBo(TenantPayoneerDefaultBo bo) {
        TenantPayoneer update = MapstructUtils.convert(bo, TenantPayoneer.class);

        Long currentId = update.getId();

        LambdaQueryWrapper<TenantPayoneer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantPayoneer::getDelFlag, "0").ne(TenantPayoneer::getId, currentId);
        ;
        TenantPayoneer notDefaultEntity = new TenantPayoneer();
        notDefaultEntity.setIsDefault(0);
        baseMapper.update(notDefaultEntity, lqw);

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 根据租户Id+账号Id去查询租户Payoneer
     * @param tenantId
     * @param accountId
     * @return
     */
    @Cacheable(cacheNames = GlobalConstants.GLOBAL_REDIS_KEY + "PAYONEER#60s", key =  "#tenantId+':'+#accountId")
    public TenantPayoneer selectByAccountIdAndTenantId(String tenantId, String accountId) {
        LambdaQueryWrapper<TenantPayoneer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantPayoneer::getTenantId, tenantId);
        lqw.eq(TenantPayoneer::getAccountId, accountId);
        lqw.orderByAsc(TenantPayoneer::getDelFlag);
        lqw.orderByDesc(TenantPayoneer::getCreateTime);
        List<TenantPayoneer> tenantPayoneers = TenantHelper.ignore(() -> baseMapper.selectPayoneers(lqw));
        if(CollUtil.isNotEmpty(tenantPayoneers)) {
            return tenantPayoneers.get(0);
        }

        return null;
    }
}
