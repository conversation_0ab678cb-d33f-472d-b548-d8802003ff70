package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.bo.SiteCountryCurrencyBo;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 站点国家币种信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-12-19
 */
@RequiredArgsConstructor
@Service
public class ISiteCountryCurrencyService extends ServiceImpl<SiteCountryCurrencyMapper, SiteCountryCurrency> {

    private final SiteCountryCurrencyMapper baseMapper;

    /**
     * 查询站点国家币种信息
     */
    public SiteCountryCurrencyVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询站点国家币种信息列表
     */
    public TableDataInfo<SiteCountryCurrencyVo> queryPageList(SiteCountryCurrencyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SiteCountryCurrency> lqw = buildQueryWrapper(bo);
        Page<SiteCountryCurrencyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询站点国家币种信息列表
     */
    public List<SiteCountryCurrencyVo> queryList(SiteCountryCurrencyBo bo) {
        LambdaQueryWrapper<SiteCountryCurrency> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SiteCountryCurrency> buildQueryWrapper(SiteCountryCurrencyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SiteCountryCurrency> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCountryCode()), SiteCountryCurrency::getCountryCode, bo.getCountryCode());
        lqw.like(StringUtils.isNotBlank(bo.getCountryName()), SiteCountryCurrency::getCountryName, bo.getCountryName());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencyCode()), SiteCountryCurrency::getCurrencyCode, bo.getCurrencyCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencySymbol()), SiteCountryCurrency::getCurrencySymbol, bo.getCurrencySymbol());
        return lqw;
    }

    /**
     * 新增站点国家币种信息
     */
    public Boolean insertByBo(SiteCountryCurrencyBo bo) {
        SiteCountryCurrency add = MapstructUtils.convert(bo, SiteCountryCurrency.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改站点国家币种信息
     */
    public Boolean updateByBo(SiteCountryCurrencyBo bo) {
        SiteCountryCurrency update = MapstructUtils.convert(bo, SiteCountryCurrency.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SiteCountryCurrency entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除站点国家币种信息
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取币种列表
     * @return
     */
    public List<SiteCountryCurrencyVo> getCurrencyList() {
        return baseMapper.getCurrencyList();
    }

    /**
     * 站点国家币种列表
     * @return
     */
    public List<SiteCountryCurrencyVo> getSiteCurrencyList(){
        return baseMapper.getSiteCurrencyList();
    }

    /**
     * 获取币种符号
     * @param countryCode
     * @return
     */
    public String getCurrencySymbolByCurrencyCode(String currencyCode){
        return baseMapper.getCurrencySymbolByCurrencyCode(currencyCode);
    }
    public SiteCountryCurrency getSiteById(Long id) {

        return baseMapper.selectById(id);
    }

    /**
     * 功能描述：按国家代码获取站点
     *
     * @param countryCode 国家代码
     * @return {@link SiteCountryCurrency }
     * <AUTHOR>
     * @date 2025/01/07
     */
    public SiteCountryCurrency getSiteByCountryCode(String countryCode) {
        List<SiteCountryCurrency> countryCurrencyList = list();
        Map<String, SiteCountryCurrency> currencyMap = countryCurrencyList.stream()
                                                                          .collect(Collectors.toMap(SiteCountryCurrency::getCountryCode, Function.identity()));
        return currencyMap.get(countryCode);
    }
    public Map<String, SiteCountryCurrency> getSiteMap() {
        List<SiteCountryCurrency> countryCurrencyList = list();
        return countryCurrencyList.stream()
                                  .collect(Collectors.toMap(SiteCountryCurrency::getCountryCode, Function.identity()));
    }

    public Map<Long, SiteCountryCurrency> getSiteIdMap() {
        List<SiteCountryCurrency> countryCurrencyList = list();
        return countryCurrencyList.stream()
                                  .collect(Collectors.toMap(SiteCountryCurrency::getId, Function.identity()));
    }

    /**
     * 功能描述：通过国家代码获取站点id
     *
     * @param countryCode 国家代码
     * @return {@link Long }
     * <AUTHOR>
     * @date 2025/02/07
     */
    public SiteCountryCurrencyVo queryByCountryCode(String countryCode) {
        LambdaQueryWrapper<SiteCountryCurrency> lqw = Wrappers.lambdaQuery();
        lqw.eq(SiteCountryCurrency::getCountryCode, countryCode);
        return baseMapper.selectVoOne(lqw);
    }
    /**
     * 功能描述：通过国家代码获取站点id
     *
     * @param countryCode 国家代码
     * @return {@link Long }
     * <AUTHOR>
     * @date 2025/02/07
     */
    public Long getSiteIdByCountryCode(String countryCode) {

        Long siteId = null;
        try {
            SiteCountryCurrency siteByCountryCode = getSiteByCountryCode(countryCode);
            siteId = siteByCountryCode.getId();
        }catch (Exception e) {
            log.error("获取站点信息失败",e);
            throw new RuntimeException("获取站点信息失败,站点信息异常:"+countryCode);
        }
        return siteId;
    }

    /**
     * 功能描述：按国家代码返回id,默认返回美国站点
     *
     * @param countryCode 国家代码
     * @return {@link Long }
     * <AUTHOR>
     * @date 2025/02/10
     */
    public Long getSiteIdByCountryCodeDefault(String countryCode) {

        Long siteId = null;
        try {
            SiteCountryCurrency siteByCountryCode = getSiteByCountryCode(countryCode);
            siteId = siteByCountryCode.getId();
        }catch (Exception e) {
            log.error("获取站点信息失败",e);
            throw new RuntimeException("获取站点信息失败,站点信息异常:"+countryCode);
        }
        return siteId;
    }
}
