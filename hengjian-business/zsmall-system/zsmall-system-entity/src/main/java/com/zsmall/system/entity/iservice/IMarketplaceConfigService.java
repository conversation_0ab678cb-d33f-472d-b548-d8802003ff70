package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.marketplaceConfig.ApplicablePageEnum;
import com.zsmall.common.enums.marketplaceConfig.ModuleTypeEnum;
import com.zsmall.system.entity.domain.MarketplaceConfig;
import com.zsmall.system.entity.mapper.MarketplaceConfigMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 商城配置表-数据库接口层
 * <AUTHOR>
 * @date 2023/9/6
 */
@Service
public class IMarketplaceConfigService extends ServiceImpl<MarketplaceConfigMapper, MarketplaceConfig> {

    @InMethodLog("根据适用页面查询商城配置")
    public List<MarketplaceConfig> queryByApplicablePage(ApplicablePageEnum applicablePageEnum) {
        return lambdaQuery().eq(MarketplaceConfig::getApplicablePage, applicablePageEnum).list();
    }

    @InMethodLog("根据模块类型查询商城配置")
    public List<MarketplaceConfig> queryByModuleType(ModuleTypeEnum moduleTypeEnum) {
        return lambdaQuery().eq(MarketplaceConfig::getModuleType, moduleTypeEnum).list();
    }

    @InMethodLog("按配置类型获取的配置数量")
    public Long countByModuleType(ModuleTypeEnum moduleType) {
        return lambdaQuery().eq(MarketplaceConfig::getModuleType, moduleType).count();
    }

    @InMethodLog("根据type查询配置")
    public List<MarketplaceConfig> getListByTypeForTop(ModuleTypeEnum moduleTypeEnum, int top) {
        List<MarketplaceConfig> list = lambdaQuery().eq(MarketplaceConfig::getModuleType, moduleTypeEnum)
            .eq(MarketplaceConfig::getShowFlag, true)
            .orderByAsc(MarketplaceConfig::getSort)
            .orderByAsc(MarketplaceConfig::getId).list();
        return list.stream().limit(top).collect(Collectors.toList());
    }

}
