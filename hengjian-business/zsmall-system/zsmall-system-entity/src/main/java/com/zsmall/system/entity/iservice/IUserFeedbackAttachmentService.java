package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.system.entity.domain.UserFeedbackAttachment;
import com.zsmall.system.entity.mapper.UserFeedbackAttachmentMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户反馈附件-数据库接口层
 * <AUTHOR>
 * @date 2023/9/11
 */
@Service
public class IUserFeedbackAttachmentService extends ServiceImpl<UserFeedbackAttachmentMapper, UserFeedbackAttachment> {

    @InMethodLog("根据用户反馈主键和附件类型查询用户反馈附件")
    public List<UserFeedbackAttachment> queryByUserFeedbackIdAndAttachmentType(Long userFeedbackId, AttachmentTypeEnum attachmentType) {
        return lambdaQuery().eq(UserFeedbackAttachment::getUserFeedbackId, userFeedbackId).eq(UserFeedbackAttachment::getAttachmentType, attachmentType).list();
    }

}
