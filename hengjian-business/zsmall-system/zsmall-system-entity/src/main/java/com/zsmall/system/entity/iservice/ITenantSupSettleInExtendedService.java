package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInExtended;
import com.zsmall.system.entity.mapper.TenantSupSettleInExtendedMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_extended(供应商入驻扩展信息表)】的数据库操作Service实现
* @createDate 2023-08-21 09:38:19
*/
@Service
@RequiredArgsConstructor
public class ITenantSupSettleInExtendedService extends ServiceImpl<TenantSupSettleInExtendedMapper, TenantSupSettleInExtended> {

    private final ITenantSupSettleInExtendedLogService iTenantSupSettleInExtendedLogService;

    @InMethodLog(value = "通过用户编码获取扩展信息")
    public TenantSupSettleInExtended getByBasicId(Long basicId) {
        if (basicId == null) {
            return null;
        }
        LambdaQueryWrapper<TenantSupSettleInExtended> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantSupSettleInExtended::getBasicId, basicId);
        return baseMapper.selectOne(queryWrapper);
    }



}




