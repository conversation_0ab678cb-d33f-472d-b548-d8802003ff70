package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInExtendedAttachment;
import com.zsmall.system.entity.mapper.TenantSupSettleInExtendedAttachmentMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_extended_attachment(供应商入驻扩展信息附件表)】的数据库操作Service实现
* @createDate 2023-08-21 09:38:19
*/
@Service
public class ITenantSupSettleInExtendedAttachmentService extends ServiceImpl<TenantSupSettleInExtendedAttachmentMapper, TenantSupSettleInExtendedAttachment> {


    @InMethodLog(value = "获取扩展信息附件")
    public TenantSupSettleInExtendedAttachment getByExtendedId(Long extendedId) {
        LambdaQueryWrapper<TenantSupSettleInExtendedAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantSupSettleInExtendedAttachment::getExtendedId, extendedId);
        return baseMapper.selectOne(queryWrapper);
    }

}




