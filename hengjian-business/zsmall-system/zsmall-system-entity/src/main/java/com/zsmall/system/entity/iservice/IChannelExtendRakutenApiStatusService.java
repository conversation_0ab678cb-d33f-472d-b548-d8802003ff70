package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.ChannelExtendRakutenApiStatus;
import com.zsmall.system.entity.mapper.ChannelExtendRakutenApiStatusMapper;
import org.springframework.stereotype.Service;

/**
 * 渠道扩展-RakutenAPI状态表-数据库接口
 * <AUTHOR>
 * @date 2023/11/14
 */
@Service
public class IChannelExtendRakutenApiStatusService extends ServiceImpl<ChannelExtendRakutenApiStatusMapper, ChannelExtendRakutenApiStatus> {

    @InMethodLog("根据渠道主键查询RakutenAPI状态")
    public ChannelExtendRakutenApiStatus queryByChannelId(Long channelId) {
        return lambdaQuery().eq(ChannelExtendRakutenApiStatus::getSalesChannelId, channelId).one();
    }

}
