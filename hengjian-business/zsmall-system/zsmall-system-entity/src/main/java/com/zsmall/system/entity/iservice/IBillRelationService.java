package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.bill.RelationFieldTypeEnum;
import com.zsmall.common.enums.bill.RelationTypeEnum;
import com.zsmall.system.entity.domain.BillRelation;
import com.zsmall.system.entity.mapper.BillRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_relation(账单关系表)】的数据库操作Service实现
 * @createDate 2022-11-28 19:39:05
 */
@Slf4j
@Service
public class IBillRelationService extends ServiceImpl<BillRelationMapper, BillRelation> {

    @InMethodLog("根据账单id查询")
    public List<BillRelation> queryByBillId(Long billId) {
        return lambdaQuery().eq(BillRelation::getBillId, billId).list();
    }

    @InMethodLog("根据账单id分类类型查询")
    public Page<BillRelation> queryByBillIdAndRelationType(Page<BillRelation> page, Long billId, String relationType) {
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> lambdaQuery().eq(BillRelation::getBillId, billId).eq(BillRelation::getRelationType, relationType)
                .orderByDesc(BillRelation::getCreateTime).orderByDesc(BillRelation::getId).page(page));
        } else {
            return lambdaQuery().eq(BillRelation::getBillId, billId).eq(BillRelation::getRelationType, relationType)
                .orderByDesc(BillRelation::getCreateTime).orderByDesc(BillRelation::getId).page(page);
        }
    }

    @InMethodLog("根据账单id分类类型查询（不分页）")
    public List<BillRelation> queryListByBillIdAndRelationType(Long billId, String relationType) {
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> lambdaQuery().eq(BillRelation::getBillId, billId).eq(BillRelation::getRelationType, relationType)
                .orderByDesc(BillRelation::getCreateTime).orderByDesc(BillRelation::getId).list());
        } else {
            return lambdaQuery().eq(BillRelation::getBillId, billId).eq(BillRelation::getRelationType, relationType)
                .orderByDesc(BillRelation::getCreateTime).orderByDesc(BillRelation::getId).list();
        }
    }

    @InMethodLog("根据账单id查询所有关系类型")
    public List<String> queryRelationTypeListByBillId(Long billId) {
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> baseMapper.queryRelationTypeListByBillId(billId));
        } else {
            return baseMapper.queryRelationTypeListByBillId(billId);
        }
    }

    @InMethodLog("计算总价")
    public BigDecimal queryRelationTypeTotalAmount(Long billId, String relationType, RelationFieldTypeEnum fieldType) {
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> baseMapper.queryRelationTypeTotalAmount(billId, relationType, fieldType.name()));
        } else {
            return baseMapper.queryRelationTypeTotalAmount(billId, relationType, fieldType.name());
        }
    }

    @InMethodLog("查询活动订单的账单关联")
    public List<BillRelation> queryActivityOrderRelation() {
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> baseMapper.queryActivityOrderRelation());
        } else {
            return baseMapper.queryActivityOrderRelation();
        }
    }

    @InMethodLog("查询是否存在指定的关系Id和关系类型")
    public Boolean hasByTargetIdAndRelationType(Long targetId, RelationTypeEnum relationTypeEnum) {
        return lambdaQuery().eq(BillRelation::getTargetId, targetId).eq(BillRelation::getRelationType, relationTypeEnum).count() > 0;
    }
}




