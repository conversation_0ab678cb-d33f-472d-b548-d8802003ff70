package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.zsmall.common.domain.bo.AttachmentBo;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.CurrencyEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.ReceiptAccountTypeEnum;
import com.zsmall.system.entity.domain.TenantReceiptAccount;
import com.zsmall.system.entity.domain.TenantReceiptAccountAttachment;
import com.zsmall.system.entity.domain.TenantReceiptAccountCredit;
import com.zsmall.system.entity.domain.TenantReceiptAccountPayoneer;
import com.zsmall.system.entity.domain.bo.receipt.TenantReceiptAccountAndPasswordBo;
import com.zsmall.system.entity.domain.bo.receipt.TenantReceiptAccountBo;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountAttachmentVo;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountCreditVo;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountPayoneerVo;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountVo;
import com.zsmall.system.entity.mapper.TenantReceiptAccountAttachmentMapper;
import com.zsmall.system.entity.mapper.TenantReceiptAccountCreditMapper;
import com.zsmall.system.entity.mapper.TenantReceiptAccountMapper;
import com.zsmall.system.entity.mapper.TenantReceiptAccountPayoneerMapper;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 收款账户Service接口
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = { @Lazy})
public class ITenantReceiptAccountService extends ServiceImpl<TenantReceiptAccountMapper, TenantReceiptAccount> {

    private final TenantReceiptAccountCreditMapper tenantReceiptAccountCreditMapper;
    private final TenantReceiptAccountAttachmentMapper tenantReceiptAccountAttachmentMapper;
    private final TenantReceiptAccountPayoneerMapper tenantReceiptAccountPayoneerMapper;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;

    /**
     * 查询收款账户
     */
    public TenantReceiptAccountVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询收款账户列表
     */
    public TableDataInfo<TenantReceiptAccountVo> queryPageList(TenantReceiptAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TenantReceiptAccount> lqw = buildQueryWrapper(bo);
        Page<TenantReceiptAccountVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 指定租户 - 查询收款账户列表
     */
    public List<TenantReceiptAccountVo> queryList(TenantReceiptAccountBo bo) {
        bo.setTenantId(LoginHelper.getTenantId());
        LambdaQueryWrapper<TenantReceiptAccount> lqw = buildQueryWrapper(bo);
//        List<TenantReceiptAccount> tenantReceiptAccounts = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        List<TenantReceiptAccount> tenantReceiptAccounts = baseMapper.selectReceiptAccounts(lqw);

        List<TenantReceiptAccountVo> tenantReceiptAccountVos = new ArrayList<>();
        if(CollUtil.isNotEmpty(tenantReceiptAccounts)) {
            tenantReceiptAccounts.forEach(bean -> {
                TenantReceiptAccountVo vo = MapstructUtils.convert(bean, TenantReceiptAccountVo.class);

                Long id = bean.getId();
                ReceiptAccountTypeEnum accountTypeEnum = bean.getAccountType();
                // 不同卡类型，查询内容不一样
                if(StrUtil.equals(accountTypeEnum.getValue(), ReceiptAccountTypeEnum.Credit.getValue())) {
                    // 银行卡
                    TenantReceiptAccountCreditVo tenantReceiptAccountCreditVo = getReceiptAccountCreditVo(id);
                    vo.setReceiptAccountCredit(tenantReceiptAccountCreditVo);
                    // 附件
                    List<TenantReceiptAccountAttachmentVo> attachmentVos = getReceiptAccountAttachmentVos(id);
                    vo.setAttachmentList(attachmentVos);
                }
                if(StrUtil.equals(accountTypeEnum.getValue(), ReceiptAccountTypeEnum.Payoneer.getValue())) {
                    TenantReceiptAccountPayoneerVo tenantReceiptAccountPayoneerVo = getReceiptAccountPayoneerVo(id);
                    vo.setReceiptAccountPayoneer(tenantReceiptAccountPayoneerVo);
                }
                tenantReceiptAccountVos.add(vo);
            });
        }


        return tenantReceiptAccountVos;
    }

    /**
     * 查询银行卡对应的附件信息
     * @param receiptAccountId
     * @return
     */
    public List<TenantReceiptAccountAttachmentVo> getReceiptAccountAttachmentVos(Long receiptAccountId) {
        LambdaQueryWrapper<TenantReceiptAccountAttachment> attachmentLqw = Wrappers.lambdaQuery();
        attachmentLqw.eq(TenantReceiptAccountAttachment::getReceiptAccountId, receiptAccountId);
        List<TenantReceiptAccountAttachmentVo> attachmentVos = tenantReceiptAccountAttachmentMapper.selectVoList(attachmentLqw);
        return attachmentVos;
    }

    /**
     * 根据主表Id查询Payoneer信息
     * @param receiptAccountId
     * @return
     */
    public TenantReceiptAccountPayoneerVo getReceiptAccountPayoneerVo(Long receiptAccountId) {
        LambdaQueryWrapper<TenantReceiptAccountPayoneer> payoneerLqw = Wrappers.lambdaQuery();
        payoneerLqw.eq(TenantReceiptAccountPayoneer::getReceiptAccountId, receiptAccountId);
        TenantReceiptAccountPayoneerVo tenantReceiptAccountPayoneerVo = tenantReceiptAccountPayoneerMapper.selectReceiptAccountPayoneer(payoneerLqw);
        return tenantReceiptAccountPayoneerVo;
    }

    /**
     * 根据主表Id查询银行卡信息
     * @param receiptAccountId
     * @return
     */
    public TenantReceiptAccountCreditVo getReceiptAccountCreditVo(Long receiptAccountId) {
        LambdaQueryWrapper<TenantReceiptAccountCredit> creditLqw = Wrappers.lambdaQuery();
        creditLqw.eq(TenantReceiptAccountCredit::getReceiptAccountId, receiptAccountId);
        TenantReceiptAccountCreditVo tenantReceiptAccountCreditVo = tenantReceiptAccountCreditMapper.selectReceiptAccountCredit(creditLqw);
        return tenantReceiptAccountCreditVo;
    }

    private LambdaQueryWrapper<TenantReceiptAccount> buildQueryWrapper(TenantReceiptAccountBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TenantReceiptAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getAccountCode()), TenantReceiptAccount::getAccountCode, bo.getAccountCode());
        lqw.eq(bo.getAccountStatus() != null, TenantReceiptAccount::getAccountStatus, bo.getAccountStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountType()), TenantReceiptAccount::getAccountType, bo.getAccountType());
        lqw.eq(bo.getUnavailabilityTime() != null, TenantReceiptAccount::getUnavailabilityTime, bo.getUnavailabilityTime());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), NoDeptTenantEntity::getTenantId, bo.getTenantId());
        lqw.eq(TenantReceiptAccount::getDelFlag, "0");
        return lqw;
    }

    /**
     * 新增收款账户
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TenantReceiptAccountBo bo) throws RStatusCodeException {
        TenantReceiptAccount add = MapstructUtils.convert(bo, TenantReceiptAccount.class);
        String accountType = bo.getAccountType();
        String password = bo.getPassword();


        boolean success = SystemEventUtils.verifyPassword(password);
        if (!success) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PASSWORD_ERROR);
        }

        ReceiptAccountTypeEnum accountTypeEnum = ReceiptAccountTypeEnum.valueOf(accountType);
        try {
            if (ObjectUtil.equals(accountTypeEnum, ReceiptAccountTypeEnum.Credit)) {
                TenantReceiptAccountBo.CreditCard creditCard = bo.getCreditCard();
                if (creditCard == null) {
                    log.error("CreditCard is null.");
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_CREDIT_NULL);
                }
                String currency = creditCard.getCurrency();
                String accountName = creditCard.getAccountName();
                String accountNumber = creditCard.getAccountNumber();
                List<AttachmentBo> attachmentList = creditCard.getAttachmentList();
                String bankAddress = creditCard.getBankAddress();
                String bankName = creditCard.getBankName();
                Long countryId = creditCard.getCountryId();
                String notes = creditCard.getNotes();
                String holderAddress = creditCard.getHolderAddress();
                String swiftCode = creditCard.getSwiftCode();

                if (StrUtil.hasBlank(accountName, accountType, currency, bankName, bankAddress, holderAddress, swiftCode, accountNumber) ||
                    CollUtil.isEmpty(attachmentList) || countryId == null) {
                    log.error("CreditCard parameter is null.");
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_CREDIT_PARAMETER_EMPTY);
                }

                CurrencyEnum currencyEnum = CurrencyEnum.valueOf(currency);
                //银行卡号长度校验
                if (accountNumber.length() < 5) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_LENGTH_NOT_ENOUGH_ERROR);
                }
                //银行卡号重复性校验
                boolean isExists = this.existCreditAccount(countryId, accountNumber);
                if (isExists) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_REPEAT_ERROR);
                }

                //收款信息
                TenantReceiptAccount accountEntity = saveTenantReceiptAccount(accountTypeEnum);
                Long accountId = accountEntity.getId();

                TenantReceiptAccountCredit creditEntity = new TenantReceiptAccountCredit();
                creditEntity.setReceiptAccountId(accountId);
                creditEntity.setCountryId(countryId);
                creditEntity.setAccountNumber(accountNumber);
                creditEntity.setBankName(bankName);
                creditEntity.setSwiftCode(swiftCode);
                creditEntity.setAccountName(accountName);
                creditEntity.setCurrency(currency);
                creditEntity.setBankAddress(bankAddress);
                creditEntity.setHolderAddress(holderAddress);
                creditEntity.setNote(notes);
                tenantReceiptAccountCreditMapper.insert(creditEntity);
                //收款附件信息
                List<TenantReceiptAccountAttachment> attachmentEntityList = new ArrayList<>();
                int sort = 0;
                for (AttachmentBo attachmentBo : attachmentList) {
                    String savePath = attachmentBo.getAttachmentSavePath();
                    String showUrl = attachmentBo.getAttachmentShowUrl();
                    String fileName = attachmentBo.getAttachmentName();
                    String attachmentType = attachmentBo.getAttachmentType();
                    String attachmentSuffix = attachmentBo.getAttachmentSuffix();
                    Integer attachmentSort = attachmentBo.getAttachmentSort();
                    Long ossId = attachmentBo.getOssId();

                    TenantReceiptAccountAttachment attachmentEntity = new TenantReceiptAccountAttachment();
                    attachmentEntity.setOssId(ossId);
                    attachmentEntity.setAttachmentName(fileName);
                    attachmentEntity.setAttachmentOriginalName(fileName);
                    attachmentEntity.setReceiptAccountId(accountId);
                    attachmentEntity.setAttachmentShowUrl(showUrl);
                    attachmentEntity.setAttachmentSavePath(savePath);
                    attachmentEntity.setAttachmentSort(attachmentSort);
                    attachmentEntity.setAttachmentType(attachmentType);
                    attachmentEntity.setAttachmentSuffix(attachmentSuffix);
                    attachmentEntity.setAttachmentType(
                        (StrUtil.equals(attachmentType, "pdf") || StrUtil.equals(attachmentType, "PDF")) ? AttachmentTypeEnum.File.getValue() : AttachmentTypeEnum.Image.getValue()
                    );
                    attachmentEntityList.add(attachmentEntity);
                }
                tenantReceiptAccountAttachmentMapper.insertBatch(attachmentEntityList);
            }

            if (ObjectUtil.equals(accountTypeEnum, ReceiptAccountTypeEnum.Payoneer)) {
                TenantReceiptAccountBo.Payoneer payoneer = bo.getPayoneer();
                if (payoneer == null || StrUtil.isBlank(payoneer.getPayoneerEmail())) {
                    log.error("Payoneer or Payoneer Email is null.");
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_PAYONEER_NULL);
                }

                String payoneerEmail = payoneer.getPayoneerEmail();
                //邮箱规则校验
                boolean email = Validator.isEmail(payoneerEmail);
                if (!email) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_EMAIL_FORMAT_ERROR);
                }
                //邮箱重复性校验
                boolean isExists = existPayoneerAccount(payoneerEmail);
                if (isExists) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_PAYONEER_EMAIL_REPEAT_ERROR);
                }

                //收款信息
                TenantReceiptAccount accountEntity = saveTenantReceiptAccount(accountTypeEnum);
                Long accountId = accountEntity.getId();
                TenantReceiptAccountPayoneer tenantReceiptAccountPayoneer = new TenantReceiptAccountPayoneer();
                tenantReceiptAccountPayoneer.setReceiptAccountId(accountId);
                tenantReceiptAccountPayoneer.setPayoneerEmail(payoneerEmail);
                tenantReceiptAccountPayoneerMapper.insert(tenantReceiptAccountPayoneer);
            }

            return true;
        } catch (RStatusCodeException e) {
            log.error("【保存收款账户】方法发生异常，{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("【保存收款账户】方法发生异常，{}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_ACCOUNT_SAVE_ERROR);
        }
    }

    @NotNull
    public TenantReceiptAccount saveTenantReceiptAccount(ReceiptAccountTypeEnum accountTypeEnum) throws RStatusCodeException {
        TenantReceiptAccount accountEntity = new TenantReceiptAccount();
        accountEntity.setAccountCode(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.ReceiptAccountNo));
        accountEntity.setAccountType(accountTypeEnum);
        accountEntity.setAccountStatus(GlobalStateEnum.Valid.getValue());
        baseMapper.insert(accountEntity);
        return accountEntity;
    }

    public boolean existCreditAccount(Long countryId, String accountNumber) {
        Map<String, Object> params = new HashMap<>();
        params.put("countryId", countryId);
        params.put("accountNumber", accountNumber);
        // 不等于空则代表有值
        Integer integer = baseMapper.existReceiptAccount(ReceiptAccountTypeEnum.Credit.getValue(), params);
        return integer != null;
    }

    public boolean existPayoneerAccount(String email) {
        Map<String, Object> params = new HashMap<>();
        params.put("email", email);
        // 不等于空则代表有值
        Integer integer = baseMapper.existReceiptAccount(ReceiptAccountTypeEnum.Payoneer.getValue(), params);
        return integer != null;
    }

    /**
     * 修改收款账户
     */
    public Boolean updateByBo(TenantReceiptAccountBo bo) {
        TenantReceiptAccount update = MapstructUtils.convert(bo, TenantReceiptAccount.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TenantReceiptAccount entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除收款账户
     */
    public Boolean deleteWithValidByIds(Collection<String> codes, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        LambdaQueryWrapper<TenantReceiptAccount> lqw = Wrappers.lambdaQuery();
        lqw.in(TenantReceiptAccount::getAccountCode, codes);
        return baseMapper.delete(lqw) > 0;
    }

    /**
     * 是否存在收款账号
     * @param code
     * @return
     */
    public boolean existReceiptAccountNo(String code) {
        LambdaQueryWrapper<TenantReceiptAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantReceiptAccount::getAccountCode, code);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 查询完整账号
     * @param bo
     * @return
     */
    public String getFullAccountNumber(TenantReceiptAccountAndPasswordBo bo) throws RStatusCodeException {
        String accountCode = bo.getAccountCode();
        String password = bo.getPassword();

        //密码校验
        boolean success = SystemEventUtils.verifyPassword(password);
        if (!success) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PASSWORD_ERROR);
        }

        // 根据租户Id + 账号编码 查询账号
        TenantReceiptAccount tenantReceiptAccount = getTenantReceiptAccount(LoginHelper.getTenantId(), accountCode);

        if(tenantReceiptAccount == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.RECEIPT_AMOUNT_NOT_FOUND_ERROR);
        }

        Long receiptAccountId = tenantReceiptAccount.getId();
        String fullAccountNumber = "";
        if (StrUtil.equals(tenantReceiptAccount.getAccountType().getValue(), ReceiptAccountTypeEnum.Credit.name())) {
            TenantReceiptAccountCreditVo receiptAccountCreditVo = this.getReceiptAccountCreditVo(receiptAccountId);
            fullAccountNumber = receiptAccountCreditVo.getAccountNumber();
        } else if (StrUtil.equals(tenantReceiptAccount.getAccountType().getValue(), ReceiptAccountTypeEnum.Payoneer.name())) {
            TenantReceiptAccountPayoneerVo receiptAccountPayoneerVo = this.getReceiptAccountPayoneerVo(receiptAccountId);
            fullAccountNumber = receiptAccountPayoneerVo.getPayoneerEmail();
        }

        return fullAccountNumber;
    }

    /**
     * 查询收款账户 - 不管状态、不管租户
     * @param accountCode
     * @return
     */
    private TenantReceiptAccount getTenantReceiptAccount(String tenantId, String accountCode) {
        LambdaQueryWrapper<TenantReceiptAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantReceiptAccount::getAccountCode, accountCode);
        lqw.eq(TenantReceiptAccount::getTenantId, tenantId);
//        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        return TenantHelper.ignore(() -> baseMapper.selectReceiptAccount(lqw));
    }

    /**
     * 删除账号
     * @param bo
     * @return
     */
    public boolean delete(TenantReceiptAccountAndPasswordBo bo) throws RStatusCodeException {
        String accountCode = bo.getAccountCode();
        String password = bo.getPassword();
        //密码校验
        boolean success = SystemEventUtils.verifyPassword(password);
        if (!success) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PASSWORD_ERROR);
        }

        LambdaQueryWrapper<TenantReceiptAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantReceiptAccount::getAccountCode, accountCode);

        TenantReceiptAccount tenantReceiptAccount = new TenantReceiptAccount();
        tenantReceiptAccount.setAccountStatus(GlobalStateEnum.Invalid.getValue());
//        tenantReceiptAccount.setDelFlag();
//        return baseMapper.delete(lqw) > 0;
        return baseMapper.update(tenantReceiptAccount, lqw) > 0;
    }
}
