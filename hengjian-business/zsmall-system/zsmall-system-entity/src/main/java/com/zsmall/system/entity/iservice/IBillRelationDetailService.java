package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.bill.RelationFieldTypeEnum;
import com.zsmall.system.entity.domain.BillRelationDetail;
import com.zsmall.system.entity.mapper.BillRelationDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【bill_relation_detail(账单关系详情表)】的数据库操作Service实现
* @createDate 2022-11-28 19:39:05
*/
@Slf4j
@Service
public class IBillRelationDetailService extends ServiceImpl<BillRelationDetailMapper, BillRelationDetail> {

  @InMethodLog("根据账单关系id查询")
  public List<BillRelationDetail> queryByBillRelationId(Long billRelationId) {
    return lambdaQuery().eq(BillRelationDetail::getBillRelationId, billRelationId).list();
  }

  @InMethodLog("根据账单关系id和字段类型查询")
  public BillRelationDetail queryByBillRelationIdAndFieldType(Long billRelationId,
    RelationFieldTypeEnum relationFieldTypeEnum) {
    return lambdaQuery().eq(BillRelationDetail::getBillRelationId, billRelationId).eq(BillRelationDetail::getFieldType, relationFieldTypeEnum).one();
  }

}




