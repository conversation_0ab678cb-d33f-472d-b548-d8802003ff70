package com.zsmall.system.entity.iservice;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.constant.MallConstants;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.system.entity.domain.TenantExtraSetting;
import com.zsmall.system.entity.domain.bo.extraSetting.TenantExtraSettingBo;
import com.zsmall.system.entity.domain.vo.extraSetting.CommonTenantExtraSettingVo;
import com.zsmall.system.entity.domain.vo.extraSetting.TenantExtraSettingVo;
import com.zsmall.system.entity.mapper.TenantExtraSettingMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 租户额外设置Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-06-15
 */
@RequiredArgsConstructor
@Service
public class ITenantExtraSettingService extends ServiceImpl<TenantExtraSettingMapper, TenantExtraSetting> {

    private final TenantExtraSettingMapper baseMapper;

    /**
     * 查询租户额外设置
     */
    public TenantExtraSettingVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询租户额外设置列表
     */
    public TableDataInfo<TenantExtraSettingVo> queryPageList(TenantExtraSettingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TenantExtraSetting> lqw = buildQueryWrapper(bo);
        Page<TenantExtraSettingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询租户额外设置列表
     */
    public List<TenantExtraSettingVo> queryList(TenantExtraSettingBo bo) {
        LambdaQueryWrapper<TenantExtraSetting> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TenantExtraSetting> buildQueryWrapper(TenantExtraSettingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TenantExtraSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSettingCode()), TenantExtraSetting::getSettingCode, bo.getSettingCode());
        lqw.eq(StringUtils.isNotBlank(bo.getSettingValue()), TenantExtraSetting::getSettingValue, bo.getSettingValue());
        lqw.eq(bo.getState() != null, TenantExtraSetting::getState, bo.getState());
        return lqw;
    }

    /**
     * 新增租户额外设置
     */
    public Boolean insertByBo(TenantExtraSettingBo bo) {
        TenantExtraSetting add = MapstructUtils.convert(bo, TenantExtraSetting.class);
        validEntityBeforeSave(add, null);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改租户额外设置
     */
    public Boolean updateByBo(TenantExtraSettingBo bo) {
//        TenantExtraSetting update = MapstructUtils.convert(bo, TenantExtraSetting.class);
        TenantExtraSetting tenantExtraSetting = this.queryBySettingCode(bo.getSettingCode());

        String settingValue = bo.getSettingValue();
        Integer state = bo.getState();

        if (StrUtil.isNotBlank(settingValue)) {
            tenantExtraSetting.setSettingValue(settingValue);
        }
        if (state != null) {
            tenantExtraSetting.setState(state);
        }

        validEntityBeforeSave(tenantExtraSetting, bo.getPassword());
        return baseMapper.updateById(tenantExtraSetting) > 0;
    }

    /**
     * 保存前的数据校验
     * @param entity
     * @param password 前端传入的支付密码，用于开启免密支付校验
     * @throws RStatusCodeException
     */
    private void validEntityBeforeSave(TenantExtraSetting entity, String password) {
        //TODO 做一些数据校验,如唯一约束
        String settingCode = entity.getSettingCode();
        String settingValue = entity.getSettingValue();
        // 判断关闭免密支付，即开启密码支付，则需设置密码，即 code = EnablePaymentPassword && value == true
        if (StrUtil.equals(settingCode, MallConstants.DistributorSettings.EnablePaymentPassword)
            && BooleanUtil.toBoolean(settingValue)) {
            TenantExtraSetting tenantExtraSetting = this.queryBySettingCode(MallConstants.DistributorSettings.PaymentPassword);
            // 无配置，或者有配置，密码为空，通通算是未设置密码
            if (tenantExtraSetting == null ||
                (tenantExtraSetting != null && StrUtil.isBlank(tenantExtraSetting.getSettingValue()))) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYMENT_PASSWORD_NOT_SET);
            }
        }

        // 开启免密支付，即关闭密码支付，需校验下支付密码，即 code = EnablePaymentPassword && value == false
        if ((StrUtil.equals(settingCode, MallConstants.DistributorSettings.EnablePaymentPassword)
                && !BooleanUtil.toBoolean(settingValue))) {
            checkPaymentPassword(password);
        }

        // 开启自动扣款，且已开启密码支付，也要验证密码
        if((StrUtil.equals(settingCode, MallConstants.DistributorSettings.AutomaticallyDeduction) && BooleanUtil.toBoolean(settingValue))) {
            TenantExtraSetting tenantExtraSetting = this.queryBySettingCode(MallConstants.DistributorSettings.EnablePaymentPassword);
            if (tenantExtraSetting != null && StrUtil.isNotBlank(tenantExtraSetting.getSettingValue()) && BooleanUtil.toBoolean(tenantExtraSetting.getSettingValue())) {
                checkPaymentPassword(password);
            }
        }
    }

    /**
     * 校验支付密码
     * @param password
     */
    private void checkPaymentPassword(String password) {
        TenantExtraSetting tenantExtraSetting = this.queryBySettingCode(MallConstants.DistributorSettings.PaymentPassword);
        if(tenantExtraSetting != null) {
            String paymentPassword = tenantExtraSetting.getSettingValue();
            //密码校验
            boolean checkpw = BCrypt.checkpw(password, paymentPassword);
            if (!checkpw) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PASSWORD_ERROR);
            }

        }
    }

    /**
     * 批量删除租户额外设置
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 查询租户扩展配置
     *
     * @param tenantId    租户id
     * @param settingCode 配置码
     * @return
     */
    public TenantExtraSetting queryBySettingCodeAndTenantId(String tenantId, String settingCode) {
        LambdaQueryWrapper<TenantExtraSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantExtraSetting::getTenantId, tenantId);
        lqw.eq(TenantExtraSetting::getSettingCode, settingCode);
        lqw.eq(TenantExtraSetting::getState, GlobalStateEnum.Valid.getValue());
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 查询租户扩展配置
     *
     * @param settingCode 配置码
     * @return
     */
    public TenantExtraSetting queryBySettingCode(String settingCode) {
        LambdaQueryWrapper<TenantExtraSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantExtraSetting::getSettingCode, settingCode);
        lqw.eq(TenantExtraSetting::getState, GlobalStateEnum.Valid.getValue());
        return baseMapper.selectOne(lqw);
    }

    /**
     * 查询常见的租户额外配置
     *
     * @return
     */
    public CommonTenantExtraSettingVo getCommonTenantExtraSettings() {
        // 默认开启无密码支付
        boolean paymentPasswordSetting = true;
        // 默认不开启自动扣款
        boolean automaticallyDeduction = false;
        // 默认无密码
        boolean havePaymentPassword = false;

        // 开启无密码支付
        TenantExtraSetting tenantExtraSetting = queryBySettingCode(MallConstants.DistributorSettings.EnablePaymentPassword);
        if (tenantExtraSetting != null) {
            String settingValue = tenantExtraSetting.getSettingValue();
            paymentPasswordSetting = BooleanUtil.toBoolean(settingValue);
        } else {
            paymentPasswordSetting = true;

            // 避免历史数据空值
            tenantExtraSetting = new TenantExtraSetting();
            tenantExtraSetting.setTenantId(LoginHelper.getTenantId());
            tenantExtraSetting.setSettingCode(MallConstants.DistributorSettings.EnablePaymentPassword);
            tenantExtraSetting.setSettingValue(Boolean.FALSE.toString());
            tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
            baseMapper.insert(tenantExtraSetting);
        }

        // 无密码
        tenantExtraSetting = queryBySettingCode(MallConstants.DistributorSettings.PaymentPassword);
        havePaymentPassword = !((tenantExtraSetting == null) ||
            (tenantExtraSetting != null && StrUtil.isBlank(tenantExtraSetting.getSettingValue())));

        // 不开启自动扣款
        tenantExtraSetting = queryBySettingCode(MallConstants.DistributorSettings.AutomaticallyDeduction);
        if (tenantExtraSetting != null) {
            String settingValue = tenantExtraSetting.getSettingValue();
            automaticallyDeduction = BooleanUtil.toBoolean(settingValue);
        } else {
            automaticallyDeduction = false;
            // 避免历史数据空值
            tenantExtraSetting = new TenantExtraSetting();
            tenantExtraSetting.setTenantId(LoginHelper.getTenantId());
            tenantExtraSetting.setSettingCode(MallConstants.DistributorSettings.AutomaticallyDeduction);
            tenantExtraSetting.setSettingValue(Boolean.FALSE.toString());
            tenantExtraSetting.setState(GlobalStateEnum.Valid.getValue());
            baseMapper.insert(tenantExtraSetting);
        }

        CommonTenantExtraSettingVo tenantExtraSettingVo = new CommonTenantExtraSettingVo();
        tenantExtraSettingVo.setPaymentPasswordSetting(paymentPasswordSetting);
        tenantExtraSettingVo.setHavePaymentPassword(havePaymentPassword);
        tenantExtraSettingVo.setAutomaticallyDeduction(automaticallyDeduction);
        return tenantExtraSettingVo;
    }

    public TenantExtraSetting queryBySettingCodeNoLogin(String settingCode,String tenantId) {
        LambdaQueryWrapper<TenantExtraSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantExtraSetting::getSettingCode, settingCode);
        lqw.eq(TenantExtraSetting::getState, GlobalStateEnum.Valid.getValue());
        lqw.eq(TenantExtraSetting::getTenantId,tenantId);
        return baseMapper.selectOne(lqw);
    }
}
