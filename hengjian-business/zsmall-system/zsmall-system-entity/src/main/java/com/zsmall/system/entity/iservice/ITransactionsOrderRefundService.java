package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TransactionsOrderRefund;
import com.zsmall.system.entity.mapper.TransactionsOrderRefundMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 交易记录-售后主单关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@RequiredArgsConstructor
@Service
public class ITransactionsOrderRefundService extends ServiceImpl<TransactionsOrderRefundMapper, TransactionsOrderRefund> {

    @InMethodLog("保存交易记录-售后主单关系")
    public void saveRelation(Long transactionId, Long orderRefundId) {
        TransactionsOrderRefund newRelation = new TransactionsOrderRefund();
        newRelation.setTransactionsId(transactionId);
        newRelation.setOrderRefundId(orderRefundId);
        this.save(newRelation);
    }

}
