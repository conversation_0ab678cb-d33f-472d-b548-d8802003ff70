package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.system.entity.domain.ShippingReturns;
import com.zsmall.system.entity.domain.bo.ShippingReturnsBo;
import com.zsmall.system.entity.domain.vo.ShippingReturnsVo;
import com.zsmall.system.entity.mapper.ShippingReturnsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 物流与退货模板-数据库层
 *
 * <AUTHOR> Li
 * @date 2023-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IShippingReturnsService extends ServiceImpl<ShippingReturnsMapper, ShippingReturns> {

    private final ShippingReturnsMapper baseMapper;
    private static final Pattern PATTERN = Pattern.compile("<.+?>", Pattern.DOTALL);

    /**
     * 查询物流与退货模板
     */
    public ShippingReturnsVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    public IPage selectVoPage(IPage page, Wrapper wrapper) {
        return baseMapper.selectVoPage(page, wrapper);
    }

    /**
     * 查询物流与退货模板
     */
    public List<ShippingReturnsVo> selectVoList(Wrapper wrapper) {
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 查询物流与退货模板列表
     */
    public List<ShippingReturnsVo> queryList(ShippingReturnsBo bo) {
        LambdaQueryWrapper<ShippingReturns> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ShippingReturns> buildQueryWrapper(ShippingReturnsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ShippingReturns> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), ShippingReturns::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getSubmitReason()), ShippingReturns::getSubmitReason, bo.getSubmitReason());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ShippingReturns::getContent, bo.getContent());
        lqw.eq(bo.getStatus() != null, ShippingReturns::getStatus, bo.getStatus());
        lqw.eq(bo.getDefaultState() != null, ShippingReturns::getDefaultState, bo.getDefaultState());
        lqw.eq(StringUtils.isNotBlank(bo.getRejectReason()), ShippingReturns::getRejectReason, bo.getRejectReason());
        lqw.eq(bo.getReviewTime() != null, ShippingReturns::getReviewTime, bo.getReviewTime());
        return lqw;
    }

    /**
     * 新增物流与退货模板
     */
    public Boolean insertByBo(ShippingReturnsBo bo) throws Exception {
        LoginUser loginUser = LoginHelper.getLoginUser();
        String tenantId = loginUser.getTenantId();
        Long userId = loginUser.getUserId();
        ShippingReturns add = MapstructUtils.convert(bo, ShippingReturns.class);
        validEntityBeforeSave(add);
        //
        add.setName("Customize Shipping & Return");
        add.setDefaultState(0);
        add.setDelFlag("0");
        add.setStatus(1L);
        add.setTenantId(tenantId);
        add.setUserId(userId);

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改物流与退货模板
     */
    public Boolean updateByBo(ShippingReturnsBo bo) throws Exception {
        ShippingReturns update = MapstructUtils.convert(bo, ShippingReturns.class);
        validEntityBeforeSave(update);

        update.setStatus(1L);
        update.setDefaultState(0);

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ShippingReturns entity) throws Exception {
        // 做一些数据校验,如唯一约束
        String content = entity.getContent();
        Matcher matcher = PATTERN.matcher(content);
        String str = matcher.replaceAll("");
        System.out.println(str);

        if (StrUtil.isBlank(str)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SHIPPING_RETURNS_CONTENT_IS_BLANK.args(str));
        }
    }

    /**
     * 批量删除物流与退货模板
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("根据租户查询商家默认的物流&退货政策模板")
    public String getDefaultShippingReturnsContentsBySupplierId(String supplierId) {
        LambdaQueryWrapper<ShippingReturns> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingReturns::getTenantId, supplierId);
        List<ShippingReturns> entityList = TenantHelper.ignore(() -> this.list(queryWrapper));
        List<ShippingReturns> defaultList = entityList.stream().filter(shippingReturns -> shippingReturns.getDefaultState() == 1).collect(Collectors.toList());

        String result = null;
        if (CollUtil.isEmpty(defaultList)) {
            ShippingReturns defaultShippingReturns = TenantHelper.ignore(() -> this.getById(0));
            if(ObjectUtil.isNotNull(defaultShippingReturns)){
                result = defaultShippingReturns.getContent();
            }
        } else {
            result = defaultList.get(0).getContent();
        }
        log.info("result = {}", result);
        return result;
    }

}
