package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.TenantSupSettleInBasic;
import com.zsmall.system.entity.domain.TenantSupSettleInBasicLog;
import com.zsmall.system.entity.mapper.TenantSupSettleInBasicLogMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_basic_log(供应商入驻基本信息日志表)】的数据库操作Service实现
* @createDate 2023-08-30 09:35:20
*/
@Service
public class ITenantSupSettleInBasicLogService extends ServiceImpl<TenantSupSettleInBasicLogMapper, TenantSupSettleInBasicLog> {

    @InMethodLog(value = "保存日志")
    public void saveLog(Long recordId, TenantSupSettleInBasic basic) {
        TenantSupSettleInBasicLog basicLog = BeanUtil.copyProperties(basic, TenantSupSettleInBasicLog.class);
        basicLog.setBasicId(basic.getId());
        basicLog.setRecordId(recordId);
        basicLog.setCreateTime(new Date());
        basicLog.setUpdateTime(new Date());
        basicLog.setId(null);
        this.save(basicLog);
    }

    @InMethodLog(value = "获取变更前数据")
    public TenantSupSettleInBasicLog getByRecordId(Long recordId, Date reviewRecordChangeDate) {
        LambdaQueryWrapper<TenantSupSettleInBasicLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSupSettleInBasicLog::getRecordId, recordId);
        wrapper.ge(TenantSupSettleInBasicLog::getCreateTime, reviewRecordChangeDate);
        wrapper.orderByDesc(TenantSupSettleInBasicLog::getCreateTime);
        List<TenantSupSettleInBasicLog> logs = TenantHelper.ignore(() -> baseMapper.selectList(wrapper), TenantType.Manager);
        return CollUtil.isNotEmpty(logs) ? logs.get(0) : null;
    }
}




