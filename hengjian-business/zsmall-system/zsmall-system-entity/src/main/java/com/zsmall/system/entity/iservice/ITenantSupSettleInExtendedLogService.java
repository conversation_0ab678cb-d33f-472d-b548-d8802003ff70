package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInExtended;
import com.zsmall.system.entity.domain.TenantSupSettleInExtendedLog;
import com.zsmall.system.entity.mapper.TenantSupSettleInExtendedLogMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_extended_log(供应商入驻扩展信息日志表)】的数据库操作Service实现
* @createDate 2023-08-30 09:35:20
*/
@Service
public class ITenantSupSettleInExtendedLogService extends ServiceImpl<TenantSupSettleInExtendedLogMapper, TenantSupSettleInExtendedLog> {


    public void saveLog(Long recordId, TenantSupSettleInExtended extended) {
        TenantSupSettleInExtendedLog extendedLog = BeanUtil.copyProperties(extended, TenantSupSettleInExtendedLog.class);
        extendedLog.setExtendedId(extended.getId());
        extendedLog.setRecordId(recordId);
        extendedLog.setCreateTime(new Date());
        extendedLog.setUpdateTime(new Date());
        extendedLog.setId(null);
        this.save(extendedLog);
    }

    @InMethodLog(value = "获取变更前数据")
    public TenantSupSettleInExtendedLog getByRecordId(Long recordId, Date reviewRecordChangeDate) {
        LambdaQueryWrapper<TenantSupSettleInExtendedLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSupSettleInExtendedLog::getRecordId, recordId);
        wrapper.ge(TenantSupSettleInExtendedLog::getCreateTime, reviewRecordChangeDate);
        wrapper.orderByDesc(TenantSupSettleInExtendedLog::getCreateTime);
        List<TenantSupSettleInExtendedLog> logs = baseMapper.selectList(wrapper);
        return CollUtil.isNotEmpty(logs) ? logs.get(0) : null;
    }

}




