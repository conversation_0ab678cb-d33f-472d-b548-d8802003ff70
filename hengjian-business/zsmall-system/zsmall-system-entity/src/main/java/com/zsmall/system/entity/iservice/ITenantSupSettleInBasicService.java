package com.zsmall.system.entity.iservice;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.TenantSupSettleInBasic;
import com.zsmall.system.entity.mapper.TenantSupSettleInBasicMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_basic(供应商入驻基本信息表)】的数据库操作Service实现
* @createDate 2023-08-21 09:38:19
*/
@Service
@RequiredArgsConstructor
public class ITenantSupSettleInBasicService extends ServiceImpl<TenantSupSettleInBasicMapper, TenantSupSettleInBasic> {

    private final ITenantSupSettleInBasicLogService iTenantSupSettleInBasicLogService;

    @InMethodLog(value = "根据用户编码获取供应商基础信息")
    public TenantSupSettleInBasic getByTenantId(String tenantId) {
        LambdaQueryWrapper<TenantSupSettleInBasic> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isBlank(tenantId)) {
            return baseMapper.selectOne(queryWrapper);
        }else {
            queryWrapper.eq(TenantSupSettleInBasic::getTenantId, tenantId);
            return TenantHelper.ignore(() -> baseMapper.selectOne(queryWrapper));
        }
    }


}




