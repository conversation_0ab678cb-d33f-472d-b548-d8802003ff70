package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TransactionsProductActivityItem;
import com.zsmall.system.entity.mapper.TransactionsProductActivityItemMapper;
import org.springframework.stereotype.Service;

@Service
public class ITransactionsProductActivityItemService extends ServiceImpl<TransactionsProductActivityItemMapper, TransactionsProductActivityItem>{

    @InMethodLog("保存交易记录-订单关系")
    public void saveRelation(Long transactionId, Long activityItemId) {
        TransactionsProductActivityItem newRelation = new TransactionsProductActivityItem();
        newRelation.setTransactionsId(transactionId);
        newRelation.setProductActivityItemId(activityItemId);
        baseMapper.insert(newRelation);
    }

}




