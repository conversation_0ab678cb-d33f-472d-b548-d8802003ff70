package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInContact;
import com.zsmall.system.entity.mapper.TenantSupSettleInContactMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_contact(供应商入驻公司联系人信息表)】的数据库操作Service实现
* @createDate 2023-08-21 09:38:19
*/
@Service
@RequiredArgsConstructor
public class ITenantSupSettleInContactService extends ServiceImpl<TenantSupSettleInContactMapper, TenantSupSettleInContact> {

    private final ITenantSupSettleInContactLogService iTenantSupSettleInContactLogService;

    @InMethodLog(value = "根据基础信息Id获取供应商公司联系人数据")
    public List<TenantSupSettleInContact> getListByBasicId(Long basicId) {
        LambdaQueryWrapper<TenantSupSettleInContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantSupSettleInContact::getBasicId, basicId);
        return baseMapper.selectList(queryWrapper);
    }


    @InMethodLog(value = "根据用户编码和联系人类型获取供应商公司联系人数据")
    public TenantSupSettleInContact getListByBasicIdAndContactType(Long basicId, String contactType) {
        LambdaQueryWrapper<TenantSupSettleInContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantSupSettleInContact::getBasicId, basicId);
        queryWrapper.eq(TenantSupSettleInContact::getContactType, contactType);
        return baseMapper.selectOne(queryWrapper);
    }


}




