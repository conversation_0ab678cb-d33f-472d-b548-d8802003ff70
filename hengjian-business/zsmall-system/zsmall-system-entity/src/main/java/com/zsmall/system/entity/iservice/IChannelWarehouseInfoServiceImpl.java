package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.bo.channel.ChannelWarehouseInfoBo;
import com.zsmall.system.entity.domain.vo.channel.ChannelWarehouseInfoVo;
import com.zsmall.system.entity.mapper.ChannelWarehouseInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 渠道仓库关联信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-11-11
 */
@RequiredArgsConstructor
@Service
public class IChannelWarehouseInfoServiceImpl extends ServiceImpl<ChannelWarehouseInfoMapper, ChannelWarehouseInfo> {

    private final ChannelWarehouseInfoMapper baseMapper;

    /**
     * 查询渠道仓库关联信息
     */
    public ChannelWarehouseInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询渠道仓库关联信息列表
     */
    public TableDataInfo<ChannelWarehouseInfoVo> queryPageList(ChannelWarehouseInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ChannelWarehouseInfo> lqw = buildQueryWrapper(bo);
        Page<ChannelWarehouseInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询渠道仓库关联信息列表
     */
    public List<ChannelWarehouseInfoVo> queryList(ChannelWarehouseInfoBo bo) {
        LambdaQueryWrapper<ChannelWarehouseInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ChannelWarehouseInfo> buildQueryWrapper(ChannelWarehouseInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ChannelWarehouseInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTenantSaleChannelId() != null, ChannelWarehouseInfo::getTenantSaleChannelId, bo.getTenantSaleChannelId());
        lqw.eq(bo.getWarehouseAdminId() != null, ChannelWarehouseInfo::getWarehouseAdminId, bo.getWarehouseAdminId());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), ChannelWarehouseInfo::getWarehouseCode, bo.getWarehouseCode());
        lqw.like(StringUtils.isNotBlank(bo.getChannelName()), ChannelWarehouseInfo::getChannelName, bo.getChannelName());
        lqw.like(StringUtils.isNotBlank(bo.getChannelWarehouseName()), ChannelWarehouseInfo::getChannelWarehouseName, bo.getChannelWarehouseName());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelWarehouseCode()), ChannelWarehouseInfo::getChannelWarehouseCode, bo.getChannelWarehouseCode());
        return lqw;
    }

    /**
     * 新增渠道仓库关联信息
     */
    public Boolean insertByBo(ChannelWarehouseInfoBo bo) {
        ChannelWarehouseInfo add = MapstructUtils.convert(bo, ChannelWarehouseInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改渠道仓库关联信息
     */
    public Boolean updateByBo(ChannelWarehouseInfoBo bo) {
        ChannelWarehouseInfo update = MapstructUtils.convert(bo, ChannelWarehouseInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ChannelWarehouseInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除渠道仓库关联信息
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据租户渠道ID删除渠道仓库关联信息
     * @param tenantSaleChannelId
     */
    public void deleteByTenantSaleChannelId(Long tenantSaleChannelId) {
        baseMapper.delete(Wrappers.<ChannelWarehouseInfo>lambdaQuery().eq(ChannelWarehouseInfo::getTenantSaleChannelId, tenantSaleChannelId));
    }

    /**
     * 根据租户渠道ID查询渠道仓库关联信息
     * @param tenantSaleChannelId
     * @return
     */
    public List<ChannelWarehouseInfo> queryListByTenantSaleChannelId(Long tenantSaleChannelId) {
        return baseMapper.selectList(Wrappers.<ChannelWarehouseInfo>lambdaQuery().eq(ChannelWarehouseInfo::getTenantSaleChannelId, tenantSaleChannelId));
    }

    /**
     * 根据渠道名称和仓库编码查询渠道仓库关联信息
     * @param channelName
     * @param channelWarehouseCode
     * @return
     */
    public List<ChannelWarehouseInfo> queryListByChannelNameAndChannelWarehouseCode(String channelName, String channelWarehouseCode) {
        return baseMapper.selectList(Wrappers.<ChannelWarehouseInfo>lambdaQuery().eq(ChannelWarehouseInfo::getChannelName, channelName).eq(ChannelWarehouseInfo::getChannelWarehouseCode, channelWarehouseCode));
    }

    /**
     * 根据租户渠道ID和渠道仓库编码查询渠道仓库关联信息
     * @param tenantSaleChannelId
     * @param channelWarehouseCode
     * @return
     */
    public List<ChannelWarehouseInfo> queryListByTenantSaleChannelIdAndChannelWarehouseCode(Long tenantSaleChannelId, String channelWarehouseCode) {
        return baseMapper.selectList(Wrappers.<ChannelWarehouseInfo>lambdaQuery().eq(ChannelWarehouseInfo::getTenantSaleChannelId, tenantSaleChannelId).eq(ChannelWarehouseInfo::getChannelWarehouseCode, channelWarehouseCode));
    }

    /**
     * 根据租户渠道ID和渠道仓库编码查询渠道仓库关联信息
     * @param tenantSaleChannelId
     * @param channelWarehouseCode
     * @return
     */
    public ChannelWarehouseInfo queryByTenantSaleChannelIdAndChannelWarehouseCode(Long tenantSaleChannelId, String channelWarehouseCode) {
        return baseMapper.selectOne(Wrappers.<ChannelWarehouseInfo>lambdaQuery().eq(ChannelWarehouseInfo::getTenantSaleChannelId, tenantSaleChannelId).eq(ChannelWarehouseInfo::getChannelWarehouseCode, channelWarehouseCode));
    }
}
