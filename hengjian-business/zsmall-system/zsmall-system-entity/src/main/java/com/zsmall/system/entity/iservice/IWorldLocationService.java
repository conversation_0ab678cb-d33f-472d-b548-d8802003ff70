package com.zsmall.system.entity.iservice;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.enums.worldLocation.LocationTypeEnum;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.bo.worldLocation.WorldLocationBo;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationSelectVo;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.mapper.WorldLocationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 世界地点Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWorldLocationService extends ServiceImpl<WorldLocationMapper, WorldLocation> {

    private final WorldLocationMapper baseMapper;

    /**
     * 查询世界地点
     */
    public WorldLocationVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询世界地点列表
     */
    public List<WorldLocationVo> queryList(WorldLocationBo bo) {
        LambdaQueryWrapper<WorldLocation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询世界地点列表（提供给下拉选使用）
     *
     * @param bo
     */
    public List<WorldLocationSelectVo> queryListForSelect(WorldLocationBo bo) {
        log.info("查询世界地点列表（提供给下拉选使用）入参 = {}", JSONUtil.toJsonStr(bo));
        return baseMapper.queryListForSelect(bo);
    }

    private LambdaQueryWrapper<WorldLocation> buildQueryWrapper(WorldLocationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WorldLocation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getParentId() != null, WorldLocation::getParentId, bo.getParentId());
        lqw.eq(bo.getLocationType() != null, WorldLocation::getLocationType, bo.getLocationType());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationAreaCode()), WorldLocation::getLocationAreaCode, bo.getLocationAreaCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), WorldLocation::getLocationName, bo.getLocationName());
        lqw.like(bo.getLocationOtherName() != null, WorldLocation::getLocationOtherName, bo.getLocationOtherName());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), WorldLocation::getLocationCode, bo.getLocationCode());
        lqw.eq(bo.getLocationZipCode() != null, WorldLocation::getLocationZipCode, bo.getLocationZipCode());
        return lqw;
    }

    /**
     * 新增世界地点
     */
    public Boolean insertByBo(WorldLocationBo bo) {
        LoginHelper.getLoginUser(TenantType.Manager);
        WorldLocation add = MapstructUtils.convert(bo, WorldLocation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增多个世界地点
     * @param boList
     */
    public Boolean insertByBoList(List<WorldLocationBo> boList) {
        List<WorldLocation> add = MapstructUtils.convert(boList, WorldLocation.class);
        // validEntityBeforeSave(add);
        boolean flag = baseMapper.insertBatch(add);
        return flag;
    }

    /**
     * 修改世界地点
     */
    public Boolean updateByBo(WorldLocationBo bo) {
        WorldLocation update = MapstructUtils.convert(bo, WorldLocation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WorldLocation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除世界地点
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据国家代号查询所有州/省编号
     * @param countryCode
     * @return
     */
    public List<String> queryChildCodeList(String countryCode) {
        log.info("进入【根据国家代号查询所有州/省编号】 countryCode = {}", countryCode);
        return baseMapper.queryChildCodeList(countryCode);
    }

    /**
     * 根据地点编号locationCode获取地点信息
     * @param locationCode
     * @return
     */
    public WorldLocation queryByLocationCode(String locationCode, LocationTypeEnum locationType) {
        LambdaQueryWrapper<WorldLocation> lqw = Wrappers.lambdaQuery();
        lqw.eq(WorldLocation::getLocationCode, locationCode);
        lqw.eq(WorldLocation::getLocationType, locationType.getCode());
        return baseMapper.selectOne(lqw);
    }

    /**
     * 获取州、省信息
     * @param parentId
     * @param locationCode
     * @param locationType
     * @return
     */
    public WorldLocation queryByParentIdAndLocationCode(Long parentId, String locationCode, LocationTypeEnum locationType) {
        LambdaQueryWrapper<WorldLocation> lqw = Wrappers.lambdaQuery();
        lqw.eq(WorldLocation::getParentId, parentId);
        lqw.eq(WorldLocation::getLocationCode, locationCode);
        lqw.eq(WorldLocation::getLocationType, locationType.getCode());
        return baseMapper.selectOne(lqw);
    }

    /**
     * 根据地点父级id获取地点信息
     * @param parentId
     * @return
     */
    public List<WorldLocation> queryByParentId(Long parentId) {
        LambdaQueryWrapper<WorldLocation> lqw = Wrappers.lambdaQuery();
        lqw.eq(WorldLocation::getParentId, parentId);
        return baseMapper.selectList(lqw);
    }

    public WorldLocation queryByZipCode(String zipCode) {
        log.info("进入【根据zipCode查询城市信息】方法， countryIdList = {}", zipCode);
        LambdaQueryWrapper<WorldLocation> lqw = new LambdaQueryWrapper<>();
        lqw.isNotNull(WorldLocation::getLocationZipCode);
        lqw.like(WorldLocation::getLocationZipCode, zipCode);
        return this.getOne(lqw);
    }

    public WorldLocation getByLocationOthName(String state) {
        return baseMapper.getByLocationOthName(state);
    }

    /**
     * 功能描述：按邮政编码v2查询 修复了原版like的问题
     *
     * @param destZipCode Dest Zip代码
     * @return {@link WorldLocation }
     * <AUTHOR>
     * @date 2024/06/14
     */
    public WorldLocation queryByZipCodeV2(String destZipCode) {
        LambdaQueryWrapper<WorldLocation> lqw = new LambdaQueryWrapper<>();
        lqw.isNotNull(WorldLocation::getLocationZipCode);
        List<WorldLocation> list = list(lqw);
        for (WorldLocation worldLocation : list) {
            JSONArray locationZipCodes = JSONUtil.parseArray(worldLocation.getLocationZipCode());
            // 判断locationZipCode中是否包含以destZipCode开头的邮编
            for (Object obj : locationZipCodes) {
                String zipCode = (String) obj;
                if (zipCode.startsWith(destZipCode)) {
                    return worldLocation;
                }
            }
        }
        return null;
    }

    /**
     * 按其他名称查询
     * @param locationOtherName
     * @return
     */
    public WorldLocation queryByLocalOthName(String locationOtherName) {
        LambdaQueryWrapper<WorldLocation> lqw = new LambdaQueryWrapper<>();
        lqw.like(WorldLocation::getLocationOtherName, locationOtherName);
        return this.getOne(lqw);
    }

//    public String transZip(String zipCode, String state, String county, String city) {
//        String zip = zipCode;
//
//        List<WorldLocation> confZipList = baseMapper.selectConfZipByStateAndCounty(state, county);
//        if (CollectionUtils.isNotEmpty(confZipList)) {
//            String prefixZip = zipCode.substring(0, 2);
//            List<WorldLocation> subConfZipList = confZipList.stream().filter(item -> item.getZip().startsWith(prefixZip)).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(subConfZipList)) {
//                String prefixCity = city.substring(0, 2);
//                ConfZip confZip = subConfZipList.stream().filter(item -> StringUtils.isNotEmpty(item.getPrimaryCity()) &&
//                    item.getPrimaryCity().startsWith(prefixCity)).findAny().orElse(null);
//                if (null != confZip) {
//                    zip = confZip.getZip().substring(0, 3) + "**";
//                } else {
//                    confZip = subConfZipList.get(0);
//                    zip = confZip.getZip().substring(0, 3) + "**";
//                }
//            }
//        }
//
//        return zip;
//    }
}
