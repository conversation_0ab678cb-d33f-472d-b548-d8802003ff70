package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInBasicAttachment;
import com.zsmall.system.entity.mapper.TenantSupSettleInBasicAttachmentMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_basic_attachment(供应商入驻基本信息附件表)】的数据库操作Service实现
* @createDate 2023-08-21 09:38:19
*/
@Service
public class ITenantSupSettleInBasicAttachmentService extends ServiceImpl<TenantSupSettleInBasicAttachmentMapper, TenantSupSettleInBasicAttachment> {


    @InMethodLog(value = "根据基础信息获取附件集合")
    public List<TenantSupSettleInBasicAttachment> getListByBasicId(Long basicId) {
        LambdaQueryWrapper<TenantSupSettleInBasicAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantSupSettleInBasicAttachment::getBasicId, basicId);
        return baseMapper.selectList(queryWrapper);
    }


    @InMethodLog(value = "根据基础信息id删除附件集合")
    public void deleteByBasicId(Long basicId) {
        LambdaQueryWrapper<TenantSupSettleInBasicAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantSupSettleInBasicAttachment::getBasicId, basicId);
        List<TenantSupSettleInBasicAttachment> tenantSupSettleInBasicAttachments = baseMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(tenantSupSettleInBasicAttachments)) {
            this.removeBatchByIds(tenantSupSettleInBasicAttachments);
        }
    }

}




