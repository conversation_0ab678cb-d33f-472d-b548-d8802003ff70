package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.transaction.ReceiptReviewStateEnum;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.system.entity.domain.TenantPayoneer;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.TransactionReceiptAttachment;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptDetailBo;
import com.zsmall.system.entity.domain.vo.transaction.RechargeReceiptStatsVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionReceiptVo;
import com.zsmall.system.entity.mapper.TransactionReceiptAttachmentMapper;
import com.zsmall.system.entity.mapper.TransactionReceiptMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 交易回执记录Service业务层处理
 *
 * <AUTHOR> @date 2023-06-14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ITransactionReceiptService extends ServiceImpl<TransactionReceiptMapper, TransactionReceipt> {

    private final TransactionReceiptAttachmentMapper transactionReceiptAttachmentMapper;
    private final ITenantPayoneerService iTenantPayoneerService;


    public boolean existTransactionReceiptNo(String code) {
        LambdaQueryWrapper<TransactionReceipt> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionReceipt::getTransactionReceiptNo, code);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 查询交易回执单
     */
    public TransactionReceiptVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询交易回执单列表
     */
    public TableDataInfo<TransactionReceiptVo> queryPageList(TransactionReceiptBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TransactionReceipt> lqw = buildQueryWrapper(bo);
        Page<TransactionReceiptVo> result = null;
        if(StrUtil.equals(LoginHelper.getTenantType(), TenantType.Manager.name())) {
            result = TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw));
        } else {
            result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        }
        List<TransactionReceiptVo> records = result.getRecords();
        List<TransactionReceiptVo> collect = records.stream().map(item -> {
            String flag = TenantHelper.ignore(()->baseMapper.getThirdChannelFlag(item.getTenantId()));
            item.setThirdChannelFlag(flag);
            return item;
        }).collect(Collectors.toList());
        result.setRecords(collect);
        return TableDataInfo.build(result);
    }

    /**
     * 查询交易回执单列表
     */
    public List<TransactionReceiptVo> queryList(TransactionReceiptBo bo) {
        LambdaQueryWrapper<TransactionReceipt> lqw = buildQueryWrapper(bo);
        List<TransactionReceiptVo> receiptVoList = new ArrayList<>();
        if(StrUtil.equals(LoginHelper.getTenantType(), TenantType.Manager.name())) {
            receiptVoList = TenantHelper.ignore(() -> baseMapper.selectVoList(lqw));
        } else {
            receiptVoList = baseMapper.selectVoList(lqw);
        }

        if(CollUtil.isNotEmpty(receiptVoList)) {
            receiptVoList.forEach(vo -> {
                String transactionMethod = vo.getTransactionMethod();
                String accountId = vo.getAccountId();

                if(StrUtil.equals(transactionMethod, TransactionMethodEnum.OnlinePayoneer.name())) {
                    TenantPayoneer tenantPayoneer = iTenantPayoneerService.selectByAccountIdAndTenantId(vo.getTenantId(), accountId);
                    if(tenantPayoneer != null) {
                        vo.setAccountName(tenantPayoneer.getAccountName());
                    }
                }
            });
        }

        return receiptVoList;
    }

    private LambdaQueryWrapper<TransactionReceipt> buildQueryWrapper(TransactionReceiptBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TransactionReceipt> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOperatorTenantId()), TransactionReceipt::getOperatorTenantId, bo.getOperatorTenantId());
        lqw.eq(bo.getTransactionsId() != null, TransactionReceipt::getTransactionsId, bo.getTransactionsId());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionReceiptNo()), TransactionReceipt::getTransactionReceiptNo, bo.getTransactionReceiptNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionType()), TransactionReceipt::getTransactionType, bo.getTransactionType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionMethod()), TransactionReceipt::getTransactionMethod, bo.getTransactionMethod());
        lqw.eq(bo.getTransactionAmount() != null, TransactionReceipt::getTransactionAmount, bo.getTransactionAmount());
//        lqw.eq(bo.getTransactionTime() != null, TransactionReceipt::getTransactionTime, bo.getTransactionTime());
//        lqw.eq(bo.getReceiptTime() != null, TransactionReceipt::getReceiptTime, bo.getReceiptTime());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountId()), TransactionReceipt::getAccountId, bo.getAccountId());
        lqw.like(StringUtils.isNotBlank(bo.getAccountName()), TransactionReceipt::getAccountName, bo.getAccountName());
        lqw.eq(bo.getReceiptAccountId() != null, TransactionReceipt::getReceiptAccountId, bo.getReceiptAccountId());
        lqw.like(StringUtils.isNotBlank(bo.getBankName()), TransactionReceipt::getBankName, bo.getBankName());
        lqw.eq(StringUtils.isNotBlank(bo.getSwiftCode()), TransactionReceipt::getSwiftCode, bo.getSwiftCode());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TransactionReceipt::getNote, bo.getNote());
        lqw.eq(StringUtils.isNotBlank(bo.getNoteManager()), TransactionReceipt::getNoteManager, bo.getNoteManager());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdChannelNo()), TransactionReceipt::getThirdChannelNo, bo.getThirdChannelNo());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdChannelAccount()), TransactionReceipt::getThirdChannelAccount, bo.getThirdChannelAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewState()), TransactionReceipt::getReviewState, bo.getReviewState());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), TransactionReceipt::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), TransactionReceipt::getCurrency, bo.getCurrency());
        Date transactionTime = bo.getTransactionTime();
        if (transactionTime != null) {
//            Date date = DateUtil.parseDate(transactionTime);
            lqw.ge(TransactionReceipt::getTransactionTime, DateUtil.beginOfDay(transactionTime))
                .le(TransactionReceipt::getTransactionTime, DateUtil.endOfDay(transactionTime));
        }
        String transactionTimeString = bo.getTransactionTimeString();
        if (StringUtils.isNotBlank(transactionTimeString)) {
            Date date = DateUtil.parseDate(transactionTimeString);
            lqw.ge(TransactionReceipt::getTransactionTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getTransactionTime, DateUtil.endOfDay(date));
        }

        Date receiptTime = bo.getReceiptTime();
        if (receiptTime != null) {
            lqw.ge(TransactionReceipt::getReceiptTime, DateUtil.beginOfDay(receiptTime))
                .le(TransactionReceipt::getReceiptTime, DateUtil.endOfDay(receiptTime))
                .eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.Accepted);
        }
        String receiptTimeString = bo.getReceiptTimeString();
        if (StringUtils.isNotBlank(receiptTimeString)) {
            Date date = DateUtil.parseDate(receiptTimeString);
            lqw.ge(TransactionReceipt::getReceiptTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getReceiptTime, DateUtil.endOfDay(date))
                .eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.Accepted);
        }

        String createTimeStringStart = bo.getCreateTimeStringStart();
        String createTimeStringEnd = bo.getCreateTimeStringEnd();
        if (StringUtils.isNotBlank(createTimeStringStart) && StringUtils.isNotBlank(createTimeStringEnd)) {
            Date dateStart = DateUtil.beginOfDay(DateUtil.parseDate(createTimeStringStart));
            Date dateEnd = DateUtil.endOfDay(DateUtil.parseDate(createTimeStringEnd));
            lqw.ge(TransactionReceipt::getCreateTime, dateStart)
                .le(TransactionReceipt::getCreateTime, dateEnd);
        }

        String thirdChannelFlag = bo.getThirdChannelFlag();
        if (StringUtils.isNotBlank(thirdChannelFlag)) {
            List<String> tenantIdList = baseMapper.getTenantIdByThirdChannelFlag(thirdChannelFlag);
            if(CollUtil.isNotEmpty(tenantIdList)){
                lqw.in(TransactionReceipt::getTenantId, tenantIdList);
            }
        }

        lqw.orderByAsc(TransactionReceipt::getCreateTime);

        return lqw;
    }

    /**
     * 新增交易回执单
     */
    public Boolean insertByBo(TransactionReceiptBo bo) {
        TransactionReceipt add = MapstructUtils.convert(bo, TransactionReceipt.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改交易回执单
     */
    public Boolean updateByBo(TransactionReceiptBo bo) {
        TransactionReceipt update = MapstructUtils.convert(bo, TransactionReceipt.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TransactionReceipt entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除交易回执单
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取充值统计
     * @return
     */
    public RechargeReceiptStatsVo getRechargeStatistics(String currency) {
        String tenantId = null;
        if(!StrUtil.equals(TenantType.Manager.name(), LoginHelper.getTenantType())) {
            tenantId = LoginHelper.getTenantId();
        }
        if(StringUtils.isBlank(currency)){
            currency = "USD";
        }
        return baseMapper.getRechargeStatistics(tenantId,currency);
    }

    /**
     * 获取回执单附件
     * @param transactionReceiptNo
     * @return
     */
    public TransactionReceiptAttachment getAttachmentShowUrl(String transactionReceiptNo) {
        LambdaQueryWrapper<TransactionReceiptAttachment> attachmentLqw = new LambdaQueryWrapper<>();
        attachmentLqw.eq(TransactionReceiptAttachment::getTransactionReceiptNo, transactionReceiptNo)
            .eq(TransactionReceiptAttachment::getDelFlag, "0");

        List<TransactionReceiptAttachment> attachments = transactionReceiptAttachmentMapper.selectList(attachmentLqw);
        if (CollUtil.isNotEmpty(attachments)) {
            return attachments.get(0);
        }
        return null;
    }

    /**
     * 查询交易回执单
     * @param receiptNo
     * @return
     */
    public TransactionReceipt selectByReceiptNoWithNotTenant(String receiptNo) {
        LambdaQueryWrapper<TransactionReceipt> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionReceipt::getTransactionReceiptNo, receiptNo);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 忽略租户 - 更新交易回执单
     * @param transactionReceipt
     * @return
     */
    public Boolean updateByIdNotTenant(TransactionReceipt transactionReceipt) {
        return TenantHelper.ignore(() -> baseMapper.insertOrUpdate(transactionReceipt));
    }


    /**
     * 不拼接租户 - 根据交易方式、审核状态和交易时间获取交易回执单表集合
     * @param transactionMethod
     * @param receiptReviewState
     * @param dateTime
     * @return
     */
    public List<TransactionReceipt> findListByTypeAndStateAndDate(TransactionMethodEnum transactionMethod,
                                                                  ReceiptReviewStateEnum receiptReviewState,
                                                                  DateTime dateTime) {
        LambdaQueryWrapper<TransactionReceipt> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionReceipt::getTransactionMethod, transactionMethod)
            .eq(TransactionReceipt::getReviewState, receiptReviewState)
            .le(TransactionReceipt::getTransactionTime, dateTime);

        return TenantHelper.ignore(() -> this.list(lqw));
    }

    /**
     * 查看交易回执单详情
     * @param bo
     * @return
     */
    public TransactionReceiptVo getTransactionReceipt(TransactionReceiptDetailBo bo) {
        String transactionNo = bo.getTransactionNo();
        String transactionReceiptNo = bo.getTransactionReceiptNo();

        LambdaQueryWrapper<TransactionReceipt> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtils.isNotBlank(transactionReceiptNo), TransactionReceipt::getTransactionReceiptNo, transactionReceiptNo);
        lqw.exists(StringUtils.isNotBlank(transactionNo), "select 1 from transaction_record tr where tr.transaction_no = {0} " +
                "and tr.id = transaction_receipt.transactions_id ",
            transactionNo);
        return TenantHelper.ignore(() -> baseMapper.selectVoOne(lqw), TenantType.Manager, TenantType.Distributor);
    }

    /**
     * 功能描述：获取交易收据以进行发送
     *
     * @param recordNo 记录编号
     * @return {@link TransactionReceipt }
     * <AUTHOR>
     * @date 2024/09/14
     */
    public TransactionReceipt getTransactionReceiptForSend(String recordNo) {
        LambdaQueryWrapper<TransactionReceipt> eq = new LambdaQueryWrapper<TransactionReceipt>()
            .eq(TransactionReceipt::getTransactionReceiptNo, recordNo)
            .eq(TransactionReceipt::getDelFlag, 0);
        return baseMapper.selectOne(eq);
    }

    /**
     * 功能描述：通过审核单编号获得找到审核通过的单
     *
     * @param no 单号
     * @return {@link TransactionReceipt }
     * <AUTHOR>
     * @date 2025/05/14
     */
    public TransactionReceipt getAcceptedByNo(String no) {
        LambdaQueryWrapper<TransactionReceipt> last = new LambdaQueryWrapper<TransactionReceipt>().eq(TransactionReceipt::getTransactionReceiptNo, no)
                                                                                                  .eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.Accepted)
                                                                                                  .last("limit 1");
        return baseMapper.selectOne(last);
    }

    /**
     * 功能描述：已审核+派安盈+收入
     *
     * @return {@link List }<{@link TransactionReceipt }>
     * <AUTHOR>
     * @date 2025/05/16
     */
    public List<TransactionReceipt> getAcceptedOnlinePayoneer(Date afterThat) {

        LambdaQueryWrapper<TransactionReceipt> allOnlinePayoneerReceipts = new LambdaQueryWrapper<TransactionReceipt>().eq(TransactionReceipt::getTransactionMethod, TransactionMethodEnum.OnlinePayoneer)
                                                                                                                       .eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.Accepted)
                                                                                                                       .eq(TransactionReceipt::getDelFlag, 0)
                                                                                                                       .eq(TransactionReceipt::getTransactionType, TransactionTypeEnum.Recharge)
            .gt(TransactionReceipt::getCreateTime,afterThat);

        return baseMapper.selectList(allOnlinePayoneerReceipts);
    }
}
