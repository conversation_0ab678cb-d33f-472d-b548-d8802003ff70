package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.TransactionRecordAttachment;
import com.zsmall.system.entity.mapper.TransactionRecordAttachmentMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 交易记录附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class ITransactionRecordAttachmentService extends ServiceImpl<TransactionRecordAttachmentMapper, TransactionRecordAttachment> {

    private final TransactionRecordAttachmentMapper baseMapper;

    public void insertBatch(List<TransactionRecordAttachment> transactionRecordAttachmentList) {
        TenantHelper.ignore(() -> baseMapper.insertBatch(transactionRecordAttachmentList));
    }
}


