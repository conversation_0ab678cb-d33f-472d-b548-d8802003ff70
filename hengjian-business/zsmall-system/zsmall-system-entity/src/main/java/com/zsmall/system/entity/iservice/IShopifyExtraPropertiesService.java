package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.ShopifyExtraProperties;
import com.zsmall.system.entity.domain.bo.salesChannel.ShopifyExtraPropertiesBo;
import com.zsmall.system.entity.domain.vo.salesChannel.ShopifyExtraPropertiesVo;
import com.zsmall.system.entity.mapper.ShopifyExtraPropertiesMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Shopify额外属性Service接口实现
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IShopifyExtraPropertiesService extends ServiceImpl<ShopifyExtraPropertiesMapper, ShopifyExtraProperties> {


    /**
     * 查询Shopify额外属性
     */
    public ShopifyExtraPropertiesVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询Shopify额外属性列表
     */
    public TableDataInfo<ShopifyExtraPropertiesVo> queryPageList(ShopifyExtraPropertiesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ShopifyExtraProperties> lqw = buildQueryWrapper(bo);
        Page<ShopifyExtraPropertiesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询Shopify额外属性列表
     */
    public List<ShopifyExtraPropertiesVo> queryList(ShopifyExtraPropertiesBo bo) {
        LambdaQueryWrapper<ShopifyExtraProperties> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ShopifyExtraProperties> buildQueryWrapper(ShopifyExtraPropertiesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ShopifyExtraProperties> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSalesChannelId() != null, ShopifyExtraProperties::getSalesChannelId, bo.getSalesChannelId());
        lqw.eq(bo.getLocationId() != null, ShopifyExtraProperties::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getFulfillmentServiceHandle()), ShopifyExtraProperties::getFulfillmentServiceHandle, bo.getFulfillmentServiceHandle());
        return lqw;
    }

    /**
     * 新增Shopify额外属性
     */
    public Boolean insertByBo(ShopifyExtraPropertiesBo bo) {
        ShopifyExtraProperties add = MapstructUtils.convert(bo, ShopifyExtraProperties.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
//            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改Shopify额外属性
     */
    public Boolean updateByBo(ShopifyExtraPropertiesBo bo) {
        ShopifyExtraProperties update = MapstructUtils.convert(bo, ShopifyExtraProperties.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ShopifyExtraProperties entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除Shopify额外属性
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据店铺名称去查询未删除的渠道授权扩展信息
     * @param shopDomain
     * @return
     */
    public ShopifyExtraPropertiesVo queryByDomain(String shopDomain) {
        return baseMapper.queryByDomain(shopDomain);
    }

    /**
     * 根据主表渠道类型去查询shopify扩展配置
     * @param salesChannelId
     * @return
     */
    public ShopifyExtraPropertiesVo selectByChannelId(Long salesChannelId) {
        LambdaQueryWrapper<ShopifyExtraProperties> lqw = Wrappers.lambdaQuery();
        lqw.eq(ShopifyExtraProperties::getSalesChannelId, salesChannelId);
        return baseMapper.selectVoOne(lqw, ShopifyExtraPropertiesVo.class);
    }
}
