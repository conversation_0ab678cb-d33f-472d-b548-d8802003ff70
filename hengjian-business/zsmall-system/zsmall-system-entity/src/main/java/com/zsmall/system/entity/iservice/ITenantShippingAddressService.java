package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.domain.SqlLocaleField;
import com.zsmall.common.enums.YesOrNoType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.worldLocation.LocationTypeEnum;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.system.entity.domain.TenantShippingAddress;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.bo.TenantShippingAddressBo;
import com.zsmall.system.entity.domain.vo.TenantShippingAddressVo;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.mapper.TenantShippingAddressMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【tenant_shipping_address(用户收货地址表)】的数据库操作Service实现
 * @createDate 2023-07-24 18:10:26
 */
@Service
public class ITenantShippingAddressService extends ServiceImpl<TenantShippingAddressMapper, TenantShippingAddress> {

    @Autowired
    private IWorldLocationService iWorldLocationService;

    /**
     * 查询用户收货地址
     */
    public TenantShippingAddressVo queryById(Long id) {
        TenantShippingAddressVo vo = baseMapper.selectVoById(id);
        String countryCode = vo.getCountryCode();
        String stateCode = vo.getStateCode();
        WorldLocation worldLocation = iWorldLocationService.queryByLocationCode(countryCode, LocationTypeEnum.Country);
        vo.setCountryId(worldLocation.getId());
        if (StrUtil.isNotBlank(stateCode)) {
            WorldLocation stateVo = iWorldLocationService.queryByLocationCode(stateCode, LocationTypeEnum.State);
            vo.setStateId(stateVo.getId());
        }
        return vo;
    }

    /**
     * 查询用户收货地址列表
     */
    public TableDataInfo<TenantShippingAddressVo> queryPageList(TenantShippingAddressBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TenantShippingAddress> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(TenantShippingAddress::getDefaultState);
        Page<TenantShippingAddressVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<TenantShippingAddressVo> records = result.getRecords();
        for (TenantShippingAddressVo record : records) {

            String countryCode = record.getCountryCode();

            String stateCode = record.getStateCode();
            WorldLocation countryLocation = iWorldLocationService.queryByLocationCode(countryCode, LocationTypeEnum.Country);

            WorldLocation stateLocation;
            if (StrUtil.isNotBlank(stateCode)) {
                stateLocation = iWorldLocationService.queryByParentIdAndLocationCode(countryLocation.getId(), stateCode, LocationTypeEnum.State);
            } else {
                stateLocation = null;
            }

            SqlLocaleField locale = SqlLocaleField.build(lang -> {
                JSONObject locationOtherName = countryLocation.getLocationOtherName();
                String country = locationOtherName.getStr(lang);

                String state;
                if (stateLocation != null) {
                    JSONObject locationOtherName1 = stateLocation.getLocationOtherName();
                    state = locationOtherName1.getStr(lang);
                } else {
                    state = record.getState();
                }

                List<String> addressList = new LinkedList<>();
                addressList.add(StrUtil.isNotBlank(record.getAddress1()) ? record.getAddress1() : null);
                addressList.add(StrUtil.isNotBlank(record.getAddress2()) ? record.getAddress2() : null);
                addressList.add(StrUtil.isNotBlank(record.getAddress3()) ? record.getAddress3() : null);
                addressList.add(record.getCity());
                addressList.add(state);
                addressList.add(country);

                CollUtil.removeNull(addressList);
                return CollUtil.join(addressList, ", ");
            });

            record.setShippingAddressLocale(JSONUtil.parseObj(locale));
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询用户收货地址列表
     */
    public List<TenantShippingAddressVo> queryList() {
        LambdaQueryWrapper<TenantShippingAddress> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(TenantShippingAddress::getDefaultState);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询用户收货地址列表
     */
    public List<TenantShippingAddress> queryList(TenantShippingAddressBo bo) {
        LambdaQueryWrapper<TenantShippingAddress> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<TenantShippingAddress> buildQueryWrapper(TenantShippingAddressBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TenantShippingAddress> lqw = Wrappers.lambdaQuery();
        /*lqw.eq(StrUtil.isNotBlank(bo.getAreaCode()), TenantShippingAddress::getAreaCode, bo.getAreaCode());
        lqw.eq(bo.getDefaultState() != null, TenantShippingAddress::getDefaultState, bo.getDefaultState());
        lqw.eq(StrUtil.isNotBlank(bo.getEmail()), TenantShippingAddress::getEmail, bo.getEmail());
        lqw.like(StrUtil.isNotBlank(bo.getFullName()), TenantShippingAddress::getFullName, bo.getFullName());
        lqw.eq(StrUtil.isNotBlank(bo.getPhoneNumber()), TenantShippingAddress::getPhoneNumber, bo.getPhoneNumber());
        lqw.eq(StrUtil.isNotBlank(bo.getShippingAddress()), TenantShippingAddress::getShippingAddress, bo.getShippingAddress());
        lqw.eq(StrUtil.isNotBlank(bo.getCity()), TenantShippingAddress::getCity, bo.getCity());
        lqw.eq(StrUtil.isNotBlank(bo.getAddress1()), TenantShippingAddress::getAddress1, bo.getAddress1());
        lqw.eq(StrUtil.isNotBlank(bo.getAddress2()), TenantShippingAddress::getAddress2, bo.getAddress2());
        lqw.eq(StrUtil.isNotBlank(bo.getZipCode()), TenantShippingAddress::getZipCode, bo.getZipCode());
        lqw.eq(bo.getIsSameMember() != null, TenantShippingAddress::getIsSameMember, bo.getIsSameMember());*/
        return lqw;
    }

    /**
     * 新增用户收货地址
     */
    @Transactional(rollbackFor = Exception.class)
    public TenantShippingAddress insertByBo(TenantShippingAddressBo bo) {
        TenantShippingAddress add = MapstructUtils.convert(bo, TenantShippingAddress.class);
        Long countryId = bo.getCountryId();

        WorldLocationVo worldLocationVo = iWorldLocationService.queryById(countryId);
        add.setCountryCode(worldLocationVo.getLocationCode());
        add.setCountry(worldLocationVo.getLocationName());
        if (ObjectUtil.isNull(bo.getStateId()) && StrUtil.isBlank(bo.getState())) {

        }
        if (ObjectUtil.isNotNull(bo.getStateId())) {
            WorldLocationVo stateVo = iWorldLocationService.queryById(bo.getStateId());
            add.setStateCode(stateVo.getLocationCode());
            add.setState(stateVo.getLocationName());
        } else {
            add.setState(bo.getState());
        }
        StringBuilder builder = new StringBuilder();
        builder.append(add.getCountry())
            .append(" ")
            .append(add.getState())
            .append(" ")
            .append(add.getCity());
        if (StrUtil.isNotBlank(add.getAddress1())) {
            builder.append(" ").append(add.getAddress1());
        }
        if (StrUtil.isNotBlank(add.getAddress2())) {
            builder.append(" ").append(add.getAddress2());
        }
        if (StrUtil.isNotBlank(add.getAddress3())) {
            builder.append(" ").append(add.getAddress3());
        }
        add.setShippingAddress(builder.toString());
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insertOrUpdate(add);
        if (flag) {
            //如果是设置为默认地址，则其他地址改为非默认
            Integer defaultState = bo.getDefaultState();
            if (defaultState.equals(YesOrNoType.True.getCode())) {
                List<TenantShippingAddress> tenantShippingAddresses = this.queryList(bo);
                for (TenantShippingAddress i: tenantShippingAddresses) {
                    if (i.getId().equals(add.getId())) {
                        continue;
                    }
                    i.setDefaultState(YesOrNoType.False.getCode());
                }
                baseMapper.updateBatchById(tenantShippingAddresses);
            }
        }
        return add;
    }

    /**
     * 修改用户收货地址
     */
    public Boolean updateByBo(TenantShippingAddressBo bo) {
        TenantShippingAddress update = MapstructUtils.convert(bo, TenantShippingAddress.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TenantShippingAddress entity) {
        // 仅US的才校验邮编
        if (StrUtil.equals(entity.getCountryCode(), "US") && !RegexUtil.matchUSPostalCode(entity.getZipCode())) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_ZIPCODE_NOT_EXIST);
        }
    }

    /**
     * 批量删除用户收货地址
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}




