package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInContact;
import com.zsmall.system.entity.domain.TenantSupSettleInContactLog;
import com.zsmall.system.entity.mapper.TenantSupSettleInContactLogMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_contact_log(供应商入驻公司联系人信息日志表)】的数据库操作Service实现
* @createDate 2023-08-30 09:35:20
*/
@Service
public class ITenantSupSettleInContactLogService extends ServiceImpl<TenantSupSettleInContactLogMapper, TenantSupSettleInContactLog> {

    public void saveLog(Long recordId, List<TenantSupSettleInContact> contacts) {
        List<TenantSupSettleInContactLog> logs = new ArrayList<>();
        contacts.stream().forEach(contact -> {
            TenantSupSettleInContactLog contactLog = BeanUtil.copyProperties(contact, TenantSupSettleInContactLog.class);
            contactLog.setContactId(contact.getId());
            contactLog.setRecordId(recordId);
            contactLog.setCreateTime(new Date());
            contactLog.setUpdateTime(new Date());
            contactLog.setId(null);
            logs.add(contactLog);
        });
        this.saveBatch(logs);
    }

    @InMethodLog(value = "获取变更前数据")
    public List<TenantSupSettleInContactLog> getByRecordId(Long recordId, Date reviewRecordChangeDate) {
        LambdaQueryWrapper<TenantSupSettleInContactLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSupSettleInContactLog::getRecordId, recordId);
        wrapper.ge(TenantSupSettleInContactLog::getCreateTime, reviewRecordChangeDate);
        wrapper.orderByDesc(TenantSupSettleInContactLog::getCreateTime);
        List<TenantSupSettleInContactLog> logs = baseMapper.selectList(wrapper);
        return CollUtil.isNotEmpty(logs) ? logs.stream().limit(3).collect(Collectors.toList()) : null;
    }
}




