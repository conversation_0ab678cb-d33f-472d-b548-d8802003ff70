package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.MarketplaceActivityConfigItem;
import com.zsmall.system.entity.mapper.MarketplaceActivityConfigItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商城活动详情表-数据库接口层
 *
 * <AUTHOR>
 * @date 2023/9/6
 */
@Service
public class IMarketplaceActivityConfigItemService extends ServiceImpl<MarketplaceActivityConfigItemMapper, MarketplaceActivityConfigItem> {

    @InMethodLog("根据Key和商城活动编号查询商城活动详情")
    public List<MarketplaceActivityConfigItem> queryByKeyAndCode(String key, String activityCode) {
        return lambdaQuery().eq(MarketplaceActivityConfigItem::getMpActivityKey, key)
            .eq(MarketplaceActivityConfigItem::getMpActivityCode, activityCode).list();
    }

    @InMethodLog("根据商城活动编号查询商城活动详情")
    public List<MarketplaceActivityConfigItem> queryByActivityCode(String mpActivityCode) {
        return lambdaQuery().eq(MarketplaceActivityConfigItem::getMpActivityCode, mpActivityCode).list();
    }

    @InMethodLog("根据商城活动编号删除")
    public Boolean deleteByActivityCode(String mpActivityCode) {
        LambdaQueryWrapper<MarketplaceActivityConfigItem> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MarketplaceActivityConfigItem::getMpActivityCode, mpActivityCode);
        return baseMapper.delete(lqw) > 0;
    }

}
