package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.ChannelExtendRakutenGenre;
import com.zsmall.system.entity.mapper.ChannelExtendRakutenGenreMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 渠道扩展-Rakuten品类表-数据库接口
 * <AUTHOR>
 * @date 2023/10/26
 */
@Service
public class IChannelExtendRakutenGenreService extends ServiceImpl<ChannelExtendRakutenGenreMapper, ChannelExtendRakutenGenre> {

    @InMethodLog("是否存在子节点")
    public Boolean existsByParentId(String parentGenreId) {
        LambdaQueryWrapper<ChannelExtendRakutenGenre> childNode = Wrappers.lambdaQuery();
        childNode.eq(ChannelExtendRakutenGenre::getParentGenreId, parentGenreId);
        return baseMapper.exists(childNode);
    }

    @InMethodLog("查询所有品类")
    public Page<ChannelExtendRakutenGenre> queryAll(Page<ChannelExtendRakutenGenre> page) {
        return lambdaQuery().page(page);
    }

    @InMethodLog("根据品类ID查询")
    public ChannelExtendRakutenGenre queryByGenreId(String genreId) {
        return lambdaQuery().eq(ChannelExtendRakutenGenre::getGenreId, genreId).one();
    }

    @InMethodLog("根据品类ID删除")
    public Boolean removeByGenreId(List<String> genreIds) {
        LambdaQueryWrapper<ChannelExtendRakutenGenre> lqw = new LambdaQueryWrapper<>();
        lqw.in(ChannelExtendRakutenGenre::getGenreId, genreIds);
        return baseMapper.delete(lqw) > 0;
    }

    @InMethodLog("根据所有有效的品类ID")
    public List<String> queryAllGenreId() {
        return baseMapper.queryAllGenreId();
    }

}
