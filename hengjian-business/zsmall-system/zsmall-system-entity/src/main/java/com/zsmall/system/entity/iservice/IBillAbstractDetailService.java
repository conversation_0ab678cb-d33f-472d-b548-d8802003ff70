package com.zsmall.system.entity.iservice;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.bill.AbstractFieldTypeEnum;
import com.zsmall.system.entity.domain.BillAbstractDetail;
import com.zsmall.system.entity.mapper.BillAbstractDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_abstract_detail(账单摘要详情表)】的数据库操作Service实现
 * @createDate 2022-11-28 19:39:05
 */
@Slf4j
@Service
public class IBillAbstractDetailService extends ServiceImpl<BillAbstractDetailMapper, BillAbstractDetail> {

    @InMethodLog("根据实体查询")
    public List<BillAbstractDetail> queryByEntity(BillAbstractDetail queryEntity) {
        LambdaQueryWrapper<BillAbstractDetail> queryWrapper = getQueryWrapper(queryEntity);
        return this.list(queryWrapper);
    }

    @InMethodLog("根据摘要总表id查询")
    public List<BillAbstractDetail> queryByBillAbstractId(Long billAbstractId) {
        return lambdaQuery().eq(BillAbstractDetail::getBillAbstractId, billAbstractId).list();
    }

    @InMethodLog("根据摘要总表id和字段类型查询（没有查询到会返回一个新对象）")
    public BillAbstractDetail queryByBillAbstractIdAndFieldType(Long billAbstractId,
                                                                AbstractFieldTypeEnum abstractFieldTypeEnum) {
        BillAbstractDetail detail = this.lambdaQuery().eq(BillAbstractDetail::getBillAbstractId, billAbstractId)
            .eq(BillAbstractDetail::getFieldType, abstractFieldTypeEnum).one();
        if (detail == null) {
            detail = new BillAbstractDetail();
            detail.setBillAbstractId(billAbstractId);
            detail.setFieldType(abstractFieldTypeEnum);
            detail.setFieldValue(BigDecimal.ZERO);
        }
        return detail;
    }

    private LambdaQueryWrapper<BillAbstractDetail> getQueryWrapper(BillAbstractDetail queryEntity) {
        LambdaQueryWrapper<BillAbstractDetail> queryWrapper = new LambdaQueryWrapper<>();

        Long billAbstractId = queryEntity.getBillAbstractId();
        AbstractFieldTypeEnum fieldType = queryEntity.getFieldType();
        BigDecimal fieldValue = queryEntity.getFieldValue();

        queryWrapper.eq(ObjectUtil.isNotNull(billAbstractId), BillAbstractDetail::getBillAbstractId, billAbstractId)
            .eq(ObjectUtil.isNotNull(fieldType), BillAbstractDetail::getFieldType, fieldType)
            .eq(ObjectUtil.isNotNull(fieldValue), BillAbstractDetail::getFieldValue, fieldValue);
        return queryWrapper;
    }


}




