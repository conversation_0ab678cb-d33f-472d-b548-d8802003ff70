package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TransactionsOrders;
import com.zsmall.system.entity.mapper.TransactionsOrdersMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 交易记录-订单关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@RequiredArgsConstructor
@Service
public class ITransactionsOrdersService extends ServiceImpl<TransactionsOrdersMapper, TransactionsOrders> {

    @InMethodLog("保存交易记录-订单关系")
    public void saveRelation(Long transactionId, Long orderId) {
        TransactionsOrders newRelation = new TransactionsOrders();
        newRelation.setTransactionsId(transactionId);
        newRelation.setOrderId(orderId);
        this.save(newRelation);
    }

}
