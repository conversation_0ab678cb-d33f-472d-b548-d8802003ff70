package com.zsmall.system.entity.iservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.funds.TransactionsQueryBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionRecordBo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionRecordExportVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionRecordVo;
import com.zsmall.system.entity.mapper.TransactionRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 交易记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ITransactionRecordService extends ServiceImpl<TransactionRecordMapper, TransactionRecord> {

    private final TransactionRecordMapper baseMapper;

    /**
     * 查询交易记录
     */
    public TransactionRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询交易记录列表
     */
    public TableDataInfo<TransactionRecordVo> queryPageList(TransactionRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TransactionRecord> lqw = buildQueryWrapper(bo);
        Page<TransactionRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询交易记录列表
     */
    public List<TransactionRecordVo> queryList(TransactionRecordBo bo) {
        LambdaQueryWrapper<TransactionRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TransactionRecord> buildQueryWrapper(TransactionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TransactionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionNo()), TransactionRecord::getTransactionNo, bo.getTransactionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionType()), TransactionRecord::getTransactionType, bo.getTransactionType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionSubType()), TransactionRecord::getTransactionSubType, bo.getTransactionSubType());
        lqw.eq(bo.getBeforeBalance() != null, TransactionRecord::getBeforeBalance, bo.getBeforeBalance());
        lqw.eq(bo.getTransactionAmount() != null, TransactionRecord::getTransactionAmount, bo.getTransactionAmount());
        lqw.eq(bo.getAfterBalance() != null, TransactionRecord::getAfterBalance, bo.getAfterBalance());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionNote()), TransactionRecord::getTransactionNote, bo.getTransactionNote());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionState()), TransactionRecord::getTransactionState, bo.getTransactionState());

        String transactionTimeString = bo.getTransactionTimeString();
        if (StringUtils.isNotBlank(transactionTimeString)) {
            Date date = DateUtil.parseDate(transactionTimeString);
            lqw.ge(TransactionRecord::getTransactionTime, DateUtil.beginOfDay(date)).le(TransactionRecord::getTransactionTime, DateUtil.endOfDay(date));
        }
        return lqw;
    }

    /**
     * 根据实体类更新（管理员用户排除租户，其余用户会携带租户条件）
     * @param transactionRecord
     * @return
     */
    public Boolean insertOrUpdateByEntity(TransactionRecord transactionRecord) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        log.info("根据实体类更新（管理员用户排除租户，其余用户会携带租户条件） transactionRecord = {}， 当前操作人类型 = {}", JSONUtil.toJsonStr(transactionRecord), tenantTypeEnum);
        if (TenantType.Manager.equals(tenantTypeEnum)) {
            return TenantHelper.ignore(() -> baseMapper.insertOrUpdate(transactionRecord));
        } else {
            return baseMapper.insertOrUpdate(transactionRecord);
        }
    }

    public boolean existTransactionNo(String code) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getTransactionNo, code);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    public boolean existPayoneerOrderNo(String code) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getTransactionNo, code)
            .eq(TransactionRecord::getTransactionType, TransactionTypeEnum.Recharge);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 不拼接租户 - 根据交易编号查询交易记录
     * @param transactionNo
     * @return
     */
    public TransactionRecord findByTransactionNoWithNotTenant(String transactionNo) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getTransactionNo, transactionNo);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 忽略租户 - 根据交易编号和交易状态查询
     *
     * @param transactionNo
     * @param transactionStateEnums
     * @return
     */
    public TransactionRecord findByTransactionNoAndTransactionStateEnumWithNotTenant(String transactionNo,TransactionStateEnum ... transactionStateEnums) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getTransactionNo, transactionNo).in(TransactionRecord::getTransactionState, Arrays.asList(transactionStateEnums));
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 不拼接租户 - 根据交易id查询交易记录
     * @param transactionId
     * @return
     */
    public TransactionRecord findByTransactionIdWithNotTenant(Long transactionId) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getId, transactionId);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 根据交易编号查询交易记录
     * @param transactionNo
     * @return
     */
    public TransactionRecord findByTransactionNo(String transactionNo) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getTransactionNo, transactionNo);
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        } else {
            return baseMapper.selectOne(lqw);
        }
    }

    /**
     * 统计某个时间段的交易金额
     * @param transactionTypeEnum     主交易类型
     * @param transactionSubTypeEnum  子交易类型
     * @param startDate               开始时间
     * @param endDate                 结束时间
     * @return
     */
    public BigDecimal sumTransactionAmount(TransactionTypeEnum transactionTypeEnum, TransactionSubTypeEnum transactionSubTypeEnum, Date startDate, Date endDate) {
        return baseMapper.sumTransactionAmount(transactionTypeEnum, transactionSubTypeEnum, startDate, endDate);
    }

    /**
     * 查询最新的一条记录
     * @param tenantId
     * @param transactionType
     * @param transactionState
     * @return
     */
    public TransactionRecord selectTheLatestRecord(String tenantId, TransactionTypeEnum transactionType, TransactionStateEnum transactionState, String currency) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getTenantId, tenantId);
        lqw.eq(TransactionRecord::getTransactionType, transactionType.getValue());
        lqw.eq(TransactionRecord::getTransactionState, transactionState.getValue());
        lqw.eq(TransactionRecord::getCurrency, currency);
        lqw.orderByDesc(TransactionRecord::getTransactionTime);
        lqw.orderByDesc(TransactionRecord::getCreateTime);

        lqw.last("limit 1");
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    public List<TransactionRecord> selectListNotSysUserDel(TransactionsQueryBo transactionsQueryBo, int page , int pageSize) {
        return baseMapper.selectListNotSysUserDel(transactionsQueryBo,page,pageSize);
    }

    public int selectListNotSysUserCount(TransactionsQueryBo transactionsQueryBo) {
        return baseMapper.selectListNotSysUserCount(transactionsQueryBo);
    }

    @InMethodLog("根据订单号查询交易记录")
    public List<TransactionRecord> listByOrderNoList(List<String> orderNoList){
        return baseMapper.listByOrderNoList(orderNoList);
    }
    @InMethodLog("条件查询交易记录")
    public Page<TransactionRecord> queryPage(TransactionsQueryBo bo, PageQuery pageQuery) {
        return baseMapper.queryPageList(pageQuery.build(),bo );
    }
}
