package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.TenantWallet;
import com.zsmall.system.entity.domain.vo.tenantWallet.TenantWalletVO;
import com.zsmall.system.entity.mapper.TenantWalletMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 租户钱包表-数据库层接口
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
@Slf4j
@Service
public class ITenantWalletService extends ServiceImpl<TenantWalletMapper, TenantWallet> {

    /**
     * 根据租户编号查询钱包（管理员用户排除租户，其余用户会携带租户条件）
     * @param tenantId
     * @return
     */
    public TenantWallet queryByTenantId(String tenantId,String currency) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        log.info("根据租户编号查询钱包 tenantId = {}，当前操作人类型 = {},币种 = {}", tenantId, tenantTypeEnum,currency);
        LambdaQueryWrapper<TenantWallet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantWallet::getTenantId, tenantId);
        lqw.eq(TenantWallet::getCurrency, currency);
        if (!TenantType.Distributor.equals(tenantTypeEnum)) {
            return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        } else {
            return baseMapper.selectOne(lqw);
        }
    }

    public TenantWallet queryByTenantIdAndCurrency(String tenantId,String currency) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        log.info("根据租户编号查询钱包 tenantId = {}，当前操作人类型 = {}", tenantId, tenantTypeEnum);
        LambdaQueryWrapper<TenantWallet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantWallet::getTenantId, tenantId);
        lqw.eq(TenantWallet::getCurrency, currency);
        if (!TenantType.Distributor.equals(tenantTypeEnum)) {
            return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        } else {
            return baseMapper.selectOne(lqw);
        }
    }

    /**
     * 根据实体类更新（管理员用户排除租户，其余用户会携带租户条件）
     * @param tenantWallet
     * @return
     */
    public Boolean updateByEntity(TenantWallet tenantWallet) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        log.info("根据实体类更新（管理员用户排除租户，其余用户会携带租户条件） tenantWallet = {}， 当前操作人类型 = {}", JSONUtil.toJsonStr(tenantWallet), tenantTypeEnum);
        if (!TenantType.Distributor.equals(tenantTypeEnum)) {
            return TenantHelper.ignore(() -> baseMapper.updateById(tenantWallet)) > 0;
        } else {
            return baseMapper.updateById(tenantWallet) > 0;
        }
    }

    /**
     * 根据租户Id查询租户钱包
     * @param tenantId
     * @return
     */
    public List<TenantWallet> queryByTenantIdWithNotTenant(String tenantId) {
        LambdaQueryWrapper<TenantWallet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantWallet::getTenantId, tenantId);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 判断租户钱包编码是否存在
     * @param walletNo
     * @return
     */
    public boolean existWalletNo(String walletNo) {
        LambdaQueryWrapper<TenantWallet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantWallet::getWalletNo, walletNo);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 统计分销商钱余额
     * @return
     */
    public BigDecimal sumDistributorBalance() {
        return baseMapper.sumDistributorBalance();
    }

    public BigDecimal sumDistributorBalanceByCurrency(String currency) {
        return baseMapper.sumDistributorBalanceByCurrency(currency);
    }

    @InMethodLog("获取钱包余额")
    public BigDecimal getWalletBalance(String tenantId, String currency) {
        if(StringUtils.isEmpty(tenantId)){
            tenantId = LoginHelper.getTenantId();
        }
        LambdaQueryWrapper<TenantWallet> twLqw = new LambdaQueryWrapper<>();
        twLqw.eq(TenantWallet::getTenantId, tenantId);
        twLqw.eq(TenantWallet::getCurrency, StringUtils.isNotBlank(currency) ? currency : "USD");
        TenantWallet tenantWallet = baseMapper.selectOne(twLqw);
        if (ObjectUtil.isNotNull(tenantWallet)) {
           return tenantWallet.getWalletBalance();
        }
        return null;
    }

    @InMethodLog("获取钱包信息")
    public List<TenantWalletVO> getWallet(String tenantId) {
        if(StringUtils.isEmpty(tenantId)){
            tenantId = LoginHelper.getTenantId();
        }
        LambdaQueryWrapper<TenantWallet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantWallet::getTenantId, tenantId);
        List<TenantWallet> tenantWalletList = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        if(CollUtil.isNotEmpty(tenantWalletList)){
            List<TenantWalletVO> tenantWalletVOList = CollUtil.newArrayList();
            tenantWalletList.forEach(tenantWallet -> {
                TenantWalletVO tenantWalletVO = new TenantWalletVO();
                BeanUtil.copyProperties(tenantWallet, tenantWalletVO);
                tenantWalletVOList.add(tenantWalletVO);
            });
            return tenantWalletVOList;
        }
        return null;
    }

    @InMethodLog("更新钱包自动支付状态")
    public void updateWalletAutoPay(TenantWalletVO tenantWalletVO) {
        TenantWallet tenantWallet = new TenantWallet();
        BeanUtil.copyProperties(tenantWalletVO, tenantWallet);
        LambdaQueryWrapper<TenantWallet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantWallet::getTenantId, tenantWallet.getTenantId());
        lqw.eq(TenantWallet::getCurrency, tenantWallet.getCurrency());
        TenantWallet oldTenantWallet = baseMapper.selectOne(lqw);
        if (ObjectUtil.isNotNull(oldTenantWallet)) {
            oldTenantWallet.setIsAutoPay(tenantWallet.getIsAutoPay());
            baseMapper.updateById(oldTenantWallet);
        }
    }
}
