package com.zsmall.system.entity.iservice;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.TenantSupSettleInReviewRecord;
import com.zsmall.system.entity.domain.dto.settleInBasic.UserSupReviewRecordDto;
import com.zsmall.system.entity.domain.dto.settleInBasic.UserSupReviewRecordParamDto;
import com.zsmall.system.entity.mapper.TenantSupSettleInReviewRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_review_record(供应商入驻信息审核记录表)】的数据库操作Service实现
* @createDate 2023-08-21 09:38:19
*/
@Service
public class ITenantSupSettleInReviewRecordService extends ServiceImpl<TenantSupSettleInReviewRecordMapper, TenantSupSettleInReviewRecord> {


    @InMethodLog(value = "获取供应商入驻审核")
    public IPage<UserSupReviewRecordDto> getListPage(Page<UserSupReviewRecordDto> page, UserSupReviewRecordParamDto dto) {
        return TenantHelper.ignore(() -> baseMapper.getListPage(page, dto));
    }


    @InMethodLog(value = "根据供应商编码获取入驻审核信息数据")
    public TenantSupSettleInReviewRecord getByTenantId(String tenantId) {
        LambdaQueryWrapper<TenantSupSettleInReviewRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isBlank(tenantId)) {
            return baseMapper.selectOne(queryWrapper);
        }else {
            queryWrapper.eq(TenantSupSettleInReviewRecord::getTenantId, tenantId);
            return TenantHelper.ignore(() -> baseMapper.selectOne(queryWrapper));
        }
    }


}




