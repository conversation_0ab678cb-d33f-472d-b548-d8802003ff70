package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInExtendedAttachment;
import com.zsmall.system.entity.domain.TenantSupSettleInExtendedAttachmentLog;
import com.zsmall.system.entity.mapper.TenantSupSettleInExtendedAttachmentLogMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【tenant_sup_settle_in_extended_attachment_log(供应商入驻扩展信息附件日志表)】的数据库操作Service实现
 * @createDate 2023-08-30 11:23:06
 */
@Service
public class ITenantSupSettleInExtendedAttachmentLogService extends ServiceImpl<TenantSupSettleInExtendedAttachmentLogMapper, TenantSupSettleInExtendedAttachmentLog> {

    public void saveLog(Long recordId, TenantSupSettleInExtendedAttachment attachment) {
        TenantSupSettleInExtendedAttachmentLog attachmentLog = BeanUtil.copyProperties(attachment, TenantSupSettleInExtendedAttachmentLog.class);
        attachmentLog.setExtendedAttachmentId(attachment.getId());
        attachmentLog.setRecordId(recordId);
        attachmentLog.setCreateTime(new Date());
        attachmentLog.setUpdateTime(new Date());
        attachmentLog.setId(null);
        this.save(attachmentLog);
    }

    @InMethodLog(value = "获取变更前数据")
    public TenantSupSettleInExtendedAttachmentLog getByRecordId(Long recordId, Date reviewRecordChangeDate) {
        LambdaQueryWrapper<TenantSupSettleInExtendedAttachmentLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSupSettleInExtendedAttachmentLog::getRecordId, recordId);
        wrapper.ge(TenantSupSettleInExtendedAttachmentLog::getCreateTime, reviewRecordChangeDate);
        return this.getOne(wrapper);
    }
}




