package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.domain.OrderItemTrackingImportRecordSystem;
import com.zsmall.system.entity.domain.bo.DownloadRecordBo;
import com.zsmall.system.entity.domain.vo.DownloadRecordVo;
import com.zsmall.system.entity.mapper.DownloadRecordMapper;
import com.zsmall.system.entity.mapper.OrderItemTrackingImportRecordSystemMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 下载记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@RequiredArgsConstructor
@Service
public class IDownloadRecordService extends ServiceImpl<DownloadRecordMapper, DownloadRecord> {

    private final OrderItemTrackingImportRecordSystemMapper orderItemTrackingImportRecordSystemMapper;

    /**
     * 查询下载记录
     */
    public DownloadRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询下载记录列表
     */
    public TableDataInfo<DownloadRecordVo> queryPageList(DownloadRecordBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<DownloadRecord> lqw = buildQueryWrapper(bo);
        Page<DownloadRecordVo> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        String tenantId = TenantHelper.getTenantId();
        if(StringUtils.isEmpty(tenantId)){
            return TableDataInfo.build();
        }
        Integer total = baseMapper.countDownloadRecordVoList(bo, tenantId);
        if(null == total || total <= 0){
            return TableDataInfo.build();
        }
        List<DownloadRecordVo> pagedDownloadRecordVoList = baseMapper.pageDownloadRecordVoList(page, bo, tenantId);
        if(CollUtil.isEmpty(pagedDownloadRecordVoList)){
            return TableDataInfo.build();
        }
//        Page<DownloadRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
//        List<DownloadRecordVo> records = result.getRecords();
        Map<String, List<Long>> typeIdMap = pagedDownloadRecordVoList.stream()
                                                   .filter(vo -> vo.getDownloadType() != null)
                                                   .collect(Collectors.groupingBy(
                                                       DownloadRecordVo::getDownloadType,
                                                       Collectors.mapping(
                                                           DownloadRecordVo::getBusinessImportExportId,
                                                           Collectors.toList()
                                                       )
                                                   ));
        Map<Long, JSONObject> idAndMsgMap = new HashMap<>();
        for (Map.Entry<String, List<Long>> entry : typeIdMap.entrySet()) {
            String table = entry.getKey();
            List<Long> ids = entry.getValue();
            if(CollUtil.isNotEmpty(ids) && DownloadTypePlusEnum.OrderItemTrackingImport.getValue().equals(table)){
                List<OrderItemTrackingImportRecordSystem> importRecords = TenantHelper.ignore(() -> orderItemTrackingImportRecordSystemMapper.queryFailedByIds(ids));
                importRecords.forEach(importRecord -> idAndMsgMap.put(importRecord.getId(), importRecord.getImportMessage()));
            }

        }
        pagedDownloadRecordVoList.forEach(vo -> {
            String downloadType = vo.getDownloadType();
            Long businessImportExportId = vo.getBusinessImportExportId();
            vo.setTaskType(DownloadTypePlusEnum.getDescriptionByValue(downloadType));
            if(ObjectUtil.isNotEmpty(businessImportExportId) && idAndMsgMap.containsKey(businessImportExportId)){
                vo.setImportMessage(idAndMsgMap.get(businessImportExportId));
            }
        });

        return TableDataInfo.build(pagedDownloadRecordVoList,total);
    }

    /**
     * 查询下载记录列表
     */
    public List<DownloadRecordVo> queryList(DownloadRecordBo bo) {
        LambdaQueryWrapper<DownloadRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DownloadRecord> buildQueryWrapper(DownloadRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DownloadRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getExpiredTime() != null, DownloadRecord::getExpiredTime, bo.getExpiredTime());
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), DownloadRecord::getFileName, bo.getFileName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSize()), DownloadRecord::getFileSize, bo.getFileSize());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSaveKey()), DownloadRecord::getFileSaveKey, bo.getFileSaveKey());
        lqw.eq(StringUtils.isNotBlank(bo.getDownloadQuery()), DownloadRecord::getDownloadQuery, bo.getDownloadQuery());
        lqw.eq(StringUtils.isNotBlank(bo.getDownloadType()), DownloadRecord::getDownloadType, bo.getDownloadType());
        lqw.eq(bo.getRecordState() != null, DownloadRecord::getRecordState, bo.getRecordState());
        return lqw;
    }

    /**
     * 新增下载记录
     */
    public Boolean insertByBo(DownloadRecordBo bo) {
        DownloadRecord add = MapstructUtils.convert(bo, DownloadRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改下载记录
     */
    public Boolean updateByBo(DownloadRecordBo bo) {
        DownloadRecord update = MapstructUtils.convert(bo, DownloadRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DownloadRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除下载记录
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("是否存在指定状态的下载记录")
    public Boolean existsByRecordState(RecordStateEnum recordState) {
        LambdaQueryWrapper<DownloadRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(DownloadRecord::getRecordState, recordState);
        return baseMapper.exists(lqw);
    }
}
