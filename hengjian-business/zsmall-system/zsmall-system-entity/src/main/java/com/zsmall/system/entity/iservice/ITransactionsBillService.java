package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.system.entity.domain.TransactionsBill;
import com.zsmall.system.entity.mapper.TransactionsBillMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 交易记录-订单关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@RequiredArgsConstructor
@Service
public class ITransactionsBillService extends ServiceImpl<TransactionsBillMapper, TransactionsBill> {


}
