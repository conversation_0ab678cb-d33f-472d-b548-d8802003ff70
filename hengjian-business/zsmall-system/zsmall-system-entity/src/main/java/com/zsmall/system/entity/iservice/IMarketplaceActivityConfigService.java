package com.zsmall.system.entity.iservice;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.MarketplaceActivityConfig;
import com.zsmall.system.entity.mapper.MarketplaceActivityConfigMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商城活动表-数据库接口层
 *
 * <AUTHOR>
 * @date 2023/9/6
 */
@Service
public class IMarketplaceActivityConfigService extends ServiceImpl<MarketplaceActivityConfigMapper, MarketplaceActivityConfig> {

    @InMethodLog("查询名称是否存在")
    public Boolean checkActivityName(Long id, String name) {
        return lambdaQuery().eq(MarketplaceActivityConfig::getMpActivityName, name).ne(id != null, MarketplaceActivityConfig::getId, id).exists();
    }

    @InMethodLog("查询编号是否存在")
    public Boolean existMpActivityCode(String mpActivityCode) {
        return baseMapper.existMpActivityCode(mpActivityCode);
    }

    @InMethodLog("根据商城活动编号查询")
    public MarketplaceActivityConfig queryMpActivityCode(String mpActivityCode) {
        return lambdaQuery().eq(MarketplaceActivityConfig::getMpActivityCode, mpActivityCode).one();
    }

    @InMethodLog("分页查询商城活动")
    public IPage<MarketplaceActivityConfig> queryPage(String mpActivityName, String mpActivityCode, IPage<MarketplaceActivityConfig> page) {
        return lambdaQuery().like(StrUtil.isNotBlank(mpActivityName), MarketplaceActivityConfig::getMpActivityName, mpActivityName)
            .like(StrUtil.isNotBlank(mpActivityCode), MarketplaceActivityConfig::getMpActivityCode, mpActivityCode).page(page);
    }

    @InMethodLog("查询当前商城活动是否被其他商城活动绑定")
    public List<MarketplaceActivityConfig> getBindActivity(Long id, String mpActivityCode) {
        String bindActivityCode = "\"activityCode\":\"" + mpActivityCode + "\"";
        return baseMapper.getBindOtherActivity(id, bindActivityCode);
    }

}
