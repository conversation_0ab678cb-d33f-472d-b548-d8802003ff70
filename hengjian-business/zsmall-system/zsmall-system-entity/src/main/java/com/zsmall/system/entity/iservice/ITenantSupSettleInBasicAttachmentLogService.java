package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.system.entity.domain.TenantSupSettleInBasicAttachment;
import com.zsmall.system.entity.domain.TenantSupSettleInBasicAttachmentLog;
import com.zsmall.system.entity.mapper.TenantSupSettleInBasicAttachmentLogMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tenant_sup_settle_in_basic_attachment_log(供应商入驻基本信息附件日志表)】的数据库操作Service实现
* @createDate 2023-08-30 11:23:06
*/
@Service
public class ITenantSupSettleInBasicAttachmentLogService extends ServiceImpl<TenantSupSettleInBasicAttachmentLogMapper, TenantSupSettleInBasicAttachmentLog> {

    public void saveLog(Long recordId, List<TenantSupSettleInBasicAttachment> attachments) {
        List<TenantSupSettleInBasicAttachmentLog> logs = new ArrayList<>();
        attachments.stream().forEach(att -> {
            TenantSupSettleInBasicAttachmentLog attachmentLog = BeanUtil.copyProperties(att, TenantSupSettleInBasicAttachmentLog.class);
            attachmentLog.setBasicAttachmentId(att.getId());
            attachmentLog.setRecordId(recordId);
            attachmentLog.setId(null);
            attachmentLog.setUpdateTime(new Date());
            attachmentLog.setCreateTime(new Date());
            logs.add(attachmentLog);
        });
        this.saveBatch(logs);
    }

    @InMethodLog(value = "获取变更前数据")
    public List<TenantSupSettleInBasicAttachmentLog> getByRecordId(Long recordId, Date reviewRecordChangeDate) {
        LambdaQueryWrapper<TenantSupSettleInBasicAttachmentLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSupSettleInBasicAttachmentLog::getRecordId, recordId);
        wrapper.ge(TenantSupSettleInBasicAttachmentLog::getCreateTime, reviewRecordChangeDate);
        return this.getBaseMapper().selectList(wrapper);
    }
}




