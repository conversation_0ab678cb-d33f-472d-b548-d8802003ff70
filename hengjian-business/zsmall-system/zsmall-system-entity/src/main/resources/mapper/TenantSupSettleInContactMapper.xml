<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantSupSettleInContactMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.TenantSupSettleInContact">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="firstName" column="first_name" jdbcType="VARCHAR"/>
            <result property="lastName" column="last_name" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
            <result property="phoneNumber" column="phone_number" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="msgAppType" column="msg_app_type" jdbcType="INTEGER"/>
            <result property="msgAppAccount" column="msg_app_account" jdbcType="VARCHAR"/>
            <result property="countryId" column="country_id" jdbcType="BIGINT"/>
            <result property="stateId" column="state_id" jdbcType="BIGINT"/>
            <result property="stateText" column="state_text" jdbcType="VARCHAR"/>
            <result property="cityText" column="city_text" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="contactType" column="contact_type" jdbcType="VARCHAR"/>
            <result property="basicId" column="basic_id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,first_name,last_name,
        name,area_code,phone_number,
        email,msg_app_type,msg_app_account,
        country_id,state_id,state_text,
        city_text,address,contact_type,
        basic_id,del_flag,create_by,
        create_time,update_by,update_time
    </sql>
</mapper>
