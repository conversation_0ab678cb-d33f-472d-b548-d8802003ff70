<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.PaymentApprovalMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.po.PaymentApproval">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="paymentNo" column="payment_no" jdbcType="VARCHAR"/>
            <result property="isDelivery" column="is_delivery" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,payment_no,is_delivery
    </sql>
</mapper>
