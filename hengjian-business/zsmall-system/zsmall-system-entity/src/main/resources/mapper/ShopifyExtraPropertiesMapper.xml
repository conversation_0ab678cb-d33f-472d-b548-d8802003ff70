<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.ShopifyExtraPropertiesMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.ShopifyExtraProperties">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="salesChannelId" column="sales_channel_id" jdbcType="BIGINT"/>
        <result property="fulfillmentServiceId" column="fulfillment_service_id" jdbcType="BIGINT"/>
        <result property="locationId" column="location_id" jdbcType="BIGINT"/>
        <result property="fulfillmentServiceHandle" column="fulfillment_service_handle" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sales_channel_id,fulfillment_service_id,
        location_id,fulfillment_service_handle,
        create_time,update_time
    </sql>

    <select id="queryByDomain" resultType="com.zsmall.system.entity.domain.vo.salesChannel.ShopifyExtraPropertiesVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM shopify_extra_properties ser
        WHERE EXISTS (
            SELECT
                1
            FROM
                tenant_sales_channel tsc
            WHERE
                tsc.channel_name = #{channelName}
                and tsc.state = 1
                AND tsc.del_flag = '0'
                AND ser.sales_channel_id = tsc.id
        )
    </select>
</mapper>
