<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantPayoneerMapper">

    <select id="selectPayoneer" resultType="com.zsmall.system.entity.domain.TenantPayoneer">
        select tp.* from tenant_payoneer tp ${ew.getCustomSqlSegment}
    </select>
    <select id="selectPayoneers" resultType="com.zsmall.system.entity.domain.TenantPayoneer">
        select tp.* from tenant_payoneer tp ${ew.getCustomSqlSegment}
    </select>
</mapper>
