<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.OrderItemTrackingImportRecordSystemMapper">
    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.OrderItemTrackingImportRecordSystem">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="importRecordNo" column="import_record_no" jdbcType="VARCHAR"/>
        <result property="importFileName" column="import_file_name" jdbcType="VARCHAR"/>
        <result property="importTrackingNum" column="import_tracking_num" jdbcType="INTEGER"/>
        <result property="importMessage" column="import_message" jdbcType="OTHER" typeHandler="com.zsmall.common.handler.HuToolJsonTypeHandler"/>
        <result property="importState" column="import_state" jdbcType="VARCHAR" />
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="queryByIds" resultType="com.zsmall.system.entity.domain.OrderItemTrackingImportRecordSystem">
        select * from order_item_tracking_import_record
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(ids)">
            where id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and del_flag=0
        </if>
    </select>
    <select id="queryFailedByIds" resultMap="BaseResultMap">
        select * from order_item_tracking_import_record where del_flag = 0 and import_state = 'Failed'
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(ids)">
            AND id in
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </select>
</mapper>
