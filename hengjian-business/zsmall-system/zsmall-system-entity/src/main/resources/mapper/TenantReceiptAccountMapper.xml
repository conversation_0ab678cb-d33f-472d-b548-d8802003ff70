<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantReceiptAccountMapper">

    <select id="existReceiptAccount" resultType="java.lang.Integer">
        select 1
        from
            tenant_receipt_account tra
        where tra.account_status = 1
            and tra.account_type = #{accountType}
            and tra.del_flag = '0'
        <if test="accountType == 'Credit'">
            and exists (
                select 1
                from
                    tenant_receipt_account_credit trac
                where trac.receipt_account_id = tra.id
                    and trac.country_id = #{params.countryId} and trac.account_number = #{params.accountNumber}
            )
        </if>
        <if test="accountType == 'Payoneer'">
            and exists (
                select 1
                from
                    tenant_receipt_account_payoneer trap
                where trap.receipt_account_id = tra.id
                    and trap.payoneer_email = #{params.email}
            )
        </if>
        LIMIT 1
    </select>

    <select id="selectReceiptAccounts" resultType="com.zsmall.system.entity.domain.TenantReceiptAccount">
        select tra.* FROM tenant_receipt_account tra ${ew.getCustomSqlSegment}
    </select>


    <select id="selectReceiptAccount" resultType="com.zsmall.system.entity.domain.TenantReceiptAccount">
        select tra.* FROM tenant_receipt_account tra ${ew.getCustomSqlSegment}
    </select>
</mapper>
