<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BillHeadMapper">

    <select id="sumTotalAmount" resultType="java.math.BigDecimal">
        select sum(total_amount)
        from bill_head
        where del_flag = 0
          and settlement_state = 1
          AND withdrawal_state = 0
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}</if>
    </select>

    <select id="sumCurrentRevolvingOrderAmount" resultType="java.math.BigDecimal">
        select sum(current_revolving_order_amount)
        from bill_head
        where settlement_state = 0
          and del_flag = 0
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}</if>
    </select>

    <update id="batchUpdateBillStatus">
        update bill_head set bill_status =#{billStatus}
        where 1=1
        <if test="billId != null and billId.size() != 0">
            and id in
            <foreach collection="billId" item="billId" open="(" close=")" separator=",">
                #{billId}
            </foreach>
        </if>
    </update>

</mapper>
