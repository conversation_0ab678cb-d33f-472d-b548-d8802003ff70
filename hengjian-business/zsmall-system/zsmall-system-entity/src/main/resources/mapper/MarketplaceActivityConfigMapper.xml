<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.MarketplaceActivityConfigMapper">

<select id="existMpActivityCode" resultType="java.lang.Boolean">
    SELECT COUNT(mac.id) FROM marketplace_activity_config mac WHERE mac.mp_activity_code = #{mpActivityCode}
</select>

<select id="getBindOtherActivity" resultType="com.zsmall.system.entity.domain.MarketplaceActivityConfig">
    SELECT mac.*
    FROM marketplace_activity_config mac
             INNER JOIN marketplace_activity_config_item maci ON mac.id = maci.mp_activity_code
    WHERE mac.id &lt;&gt; #{id}
      AND maci.mp_activity_key = 'menuList'
      AND maci.mp_activity_value LIKE CONCAT('%', #{bindActivityCode}, '%')
      AND mac.del_flag = '0'
</select>
</mapper>
