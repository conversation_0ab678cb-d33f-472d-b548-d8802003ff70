<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantSiteMapper">
    <select id="selectTenantSiteJoinCountryCurrency"
            resultType="com.zsmall.system.entity.domain.dto.TenantSiteCountryCurrency">
        select ts.tenant_id, ts.country_code, ts.channel_flag, sc.currency_code, sc.currency_symbol
        from tenant_site ts
                 left join site_country_currency sc on ts.country_code = sc.country_code
        where sc.del_flag = 0
        group by tenant_id,currency_code,currency_symbol,channel_flag;
    </select>
</mapper>
