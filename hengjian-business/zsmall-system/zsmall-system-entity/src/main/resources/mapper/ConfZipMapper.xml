<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.ConfZipMapper">

    <select id="selectConfZipByStateAndCounty" resultType="com.zsmall.system.entity.domain.ConfZip">
        select a.id,
               a.country,
               a.state_code,
               a.state_name,
               a.primary_city,
               a.zip,
               a.zip_type,
               a.acceptable_cities,
               a.unacceptable_cities,
               a.county,
               a.timezone,
               a.area_codes,
               a.latitude,
               a.longitude,
               a.world_region,
               a.decommissioned,
               a.estimated_population,
               a.notes,
               a.last_updated_stamp,
               a.created_stamp
        from conf_zip a
        where a.state_name = #{stateName}
          and a.county like concat(#{country}, '%')
    </select>
</mapper>
