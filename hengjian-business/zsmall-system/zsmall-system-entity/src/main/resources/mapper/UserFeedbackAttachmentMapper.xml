<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.UserFeedbackAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.UserFeedbackAttachment">
    <!--@mbg.generated-->
    <!--@Table user_feedback_attachment-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="oss_id" jdbcType="BIGINT" property="ossId" />
    <result column="user_feedback_id" jdbcType="BIGINT" property="userFeedbackId" />
    <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName" />
    <result column="attachment_original_name" jdbcType="VARCHAR" property="attachmentOriginalName" />
    <result column="attachment_suffix" jdbcType="VARCHAR" property="attachmentSuffix" />
    <result column="attachment_save_path" jdbcType="VARCHAR" property="attachmentSavePath" />
    <result column="attachment_show_url" jdbcType="VARCHAR" property="attachmentShowUrl" />
    <result column="attachment_sort" jdbcType="INTEGER" property="attachmentSort" />
    <result column="attachment_type" jdbcType="VARCHAR" property="attachmentType" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, oss_id, user_feedback_id, attachment_name, attachment_original_name, attachment_suffix,
    attachment_save_path, attachment_show_url, attachment_sort, attachment_type, del_flag,
    create_by, create_time, update_by, update_time
  </sql>
</mapper>
