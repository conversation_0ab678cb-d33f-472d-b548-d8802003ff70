<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TransactionReceiptMapper">


    <select id="getRechargeStatistics"
            resultType="com.zsmall.system.entity.domain.vo.transaction.RechargeReceiptStatsVo">
        SELECT
            CAST(SUM( CASE WHEN tr.review_state = 'Pending' THEN IFNULL(tr.transaction_amount,0) ELSE 0 END) AS decimal(20,2)) as pendingAmount,
            CAST(SUM( CASE WHEN tr.review_state = 'Accepted' THEN IFNULL(tr.transaction_amount,0) ELSE 0 END) AS decimal(20,2)) as solvedAmount,
            CAST(SUM( CASE WHEN tr.review_state = 'Rejected' THEN IFNULL(tr.transaction_amount,0) ELSE 0 END) AS decimal(20,2)) as rejectedAmount,
            tr.currency,
            tr.currency_symbol as currencySymbol
        FROM
            transaction_receipt tr
        WHERE
            tr.transaction_type = 'Recharge'
          and tr.currency = #{currency}
        <if test="tenantId != null">
            AND tr.tenant_id = #{tenantId}
        </if>
    </select>
    <select id="statsPlatformTotalAmount"
            resultType="com.zsmall.system.entity.domain.vo.payment.ReceiptTotalAmountVo">
        SELECT
            CAST(IFNULL(SUM(tr.transaction_amount), 0) AS decimal(20,2)) as totalAmount,
            CAST(IFNULL(SUM(tr.transaction_fee), 0) AS decimal(20,2)) as totalFee
        FROM
            transaction_receipt tr
        WHERE
            tr.review_state = 'Accepted'
        <if test="transactionType != null">
            AND tr.transaction_type = #{transactionType}
        </if>
    </select>

    <select id="sumAmountByTransactionTypeAndReviewStatus" resultType="java.math.BigDecimal">
        SELECT
            CAST(SUM(tr.transaction_amount) AS decimal(20,2)) as total
        FROM
            transaction_receipt tr
        <where>
            <if test="states != null and states.size > 0">
                AND tr.review_state IN
                <foreach collection="states" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="transactionType != null">
                AND tr.transaction_type = #{transactionType}
            </if>
        </where>
    </select>

    <select id="selectByTransactionId" resultType="com.zsmall.system.entity.domain.TransactionReceipt">
        select * from transaction_receipt tr where tr.transactions_id = #{transactionId}
    </select>
    <select id="getThirdChannelFlag" resultType="java.lang.String">
        select third_channel_flag from sys_tenant where tenant_id = #{tenantId}
    </select>

    <select id="getTenantIdByThirdChannelFlag" resultType="java.lang.String">
        select distinct tenant_id from sys_tenant where third_channel_flag like concat('%',#{thirdChannelFlag},'%')
        and del_flag = 0
        and status = 0
    </select>


</mapper>
