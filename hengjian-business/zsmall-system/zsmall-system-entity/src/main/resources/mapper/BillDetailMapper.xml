<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BillDetailsMapper">

    <select id="selectFulfilledBillDetail" resultType="com.zsmall.system.entity.domain.BillDetails">

        select
        o.order_no,
        o.pay_time as orderTime,
        oi.tenant_id,
        oi.supplier_tenant_id as supperTenantId,
        o.total_quantity as productQuantity,
        o.original_total_product_amount as productSkuPrice,
        o.original_total_operation_fee as operationFee,
        o.original_total_final_delivery_fee as finalDeliveryFee,
        o.original_actual_total_amount as orderTotalAmount,
        o.original_refund_executable_amount as orderRefundTotalAmount,
        oi.product_sku_code
        from orders o
        inner join order_item oi on o.id = oi.order_id
        where o.del_flag=0
        and oi.del_flag=0
        and oi.supplier_tenant_id=#{tenantId}
        <if test="type != null and type==1">
            and o.fulfillment_progress='Fulfilled'
        </if>
        <if test="type != null and type==2">
            and o.order_state='Refunded'
        </if>
        and o.update_time between #{startTime} and  #{endTime}
    </select>





    <select id="selectCountOrdersByTenant" resultType="java.lang.Integer">
        select count(oi.id)
        from order_item oi
                 inner join transactions_orders too on oi.order_id = too.order_id
                 inner join transaction_record trr on too.transactions_id = trr.id
        where oi.del_flag = 0
          and trr.del_flag=0
          and trr.currency=#{currencyCode}
          and trr.transaction_state != 'Processing'
          and after_balance >= 0
          and too.create_time between #{billStartTime} and #{billEndTime}
        <if test="billType != null and billType == 2">
            and oi.tenant_id = #{tenantId}</if>
        <if test="billType != null and billType == 1">
            and oi.supplier_tenant_id = #{tenantId}</if>
    </select>
    <select id="selectOrdersByTenant" resultType="com.zsmall.system.entity.domain.OrderItemSystem">
        select oi.*
        from order_item oi
        inner join transactions_orders too on oi.order_id = too.order_id
        inner join transaction_record trr on too.transactions_id = trr.id
        where oi.del_flag = 0
        and trr.del_flag=0
        and trr.currency=#{currencyCode}
        and trr.transaction_state != 'Processing'
        and after_balance >= 0
        and too.create_time between #{billStartTime} and #{billEndTime}
        <if test="billType != null and billType == 2">
            and oi.tenant_id = #{tenantId}</if>
        <if test="billType != null and billType == 1">
            and oi.supplier_tenant_id = #{tenantId}</if>
        <!--        and oi.order_no='Z174214241030183'-->
        order by oi.id desc
        limit #{startRow},#{finalPageSize}
    </select>

    <insert id="batchInsertBillDetails">
        INSERT INTO bill_details (
        id, bill_id, bill_no, order_no, tenant_id, supper_tenant_id, order_refund_no, order_time, order_status,
        order_payment_methods, product_sku_code, product_quantity, operation_fee, final_delivery_fee,
        product_sku_price, order_total_amount, order_refund_total_amount, create_by, create_time, update_by,
        update_time, del_flag,currency_symbol,currency_code,supported_logistics,unit_price
        ) VALUES
        <foreach collection="billDetails" item="item" separator=",">
            (
            #{item.id},
            #{item.billId},
            #{item.billNo},
            #{item.orderNo},
            #{item.tenantId},
            #{item.supperTenantId},
            #{item.orderRefundNo},
            #{item.orderTime},
            #{item.orderStatus},
            #{item.orderPaymentMethods},
            #{item.productSkuCode},
            #{item.productQuantity},
            #{item.operationFee},
            #{item.finalDeliveryFee},
            #{item.productSkuPrice},
            #{item.orderTotalAmount},
            #{item.orderRefundTotalAmount},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.delFlag},
            #{item.currencySymbol},
            #{item.currencyCode},
            #{item.supportedLogistics},
            #{item.unitPrice}
            )
        </foreach>
    </insert>

</mapper>
