<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BlogCategoryMapper">
    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.BlogCategory">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
        <result property="categoryLevel" column="category_level" jdbcType="INTEGER"/>
        <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
        <result property="categoryEnglishName" column="category_english_name" jdbcType="VARCHAR"/>
        <result property="categoryOtherName" column="category_other_name"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="categorySort" column="category_sort" jdbcType="INTEGER"/>
        <result property="categoryState" column="category_state" jdbcType="TINYINT"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,category_code,
        category_level,category_name,category_english_name,category_other_name,
        category_sort,category_state,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
