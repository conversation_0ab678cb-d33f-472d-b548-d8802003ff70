<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BizLocaleMessagesMapper">

    <update id="updateDelById">
        UPDATE biz_locale_messages
        SET del_flag = #{delFlag},
            del_time = #{delTime},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and del_flag = 0
    </update>

    <delete id="deleteByLangKeyList">
        delete from biz_locale_messages where del_flag = 0 and lang_key in
        <foreach collection="langKeyList" item="langKey" open="(" close=")" separator=",">
            #{langKey}
        </foreach>
    </delete>
</mapper>
