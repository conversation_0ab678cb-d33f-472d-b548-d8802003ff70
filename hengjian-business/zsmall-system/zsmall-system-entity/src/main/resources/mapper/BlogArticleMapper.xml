<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BlogArticleMapper">

    <resultMap id="BlogArticleMap" type="com.zsmall.system.entity.domain.BlogArticleEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="blogTitle" column="blog_title" jdbcType="VARCHAR"/>
            <result property="blogSubTitle" column="blog_sub_title" jdbcType="VARCHAR"/>
            <result property="blogContent" column="blog_content" jdbcType="VARCHAR"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="creatorCode" column="creator_code" jdbcType="VARCHAR"/>
            <result property="publishDate" column="publish_date" jdbcType="TIMESTAMP"/>
            <result property="coverSavePath" column="cover_save_path" jdbcType="VARCHAR"/>
            <result property="coverShowUrl" column="cover_show_url" jdbcType="VARCHAR"/>
            <result property="viewCount" column="view_count" jdbcType="INTEGER"/>
            <result property="blogStatus" column="blog_status" jdbcType="VARCHAR"/>
            <result property="language" column="language" jdbcType="VARCHAR"/>
            <result property="domain" column="domain" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,blog_title,blog_sub_title,
        blog_content,category,creator_code,
        publish_date,cover_save_path,cover_show_url,
        view_count,blog_status,language,
        domain,create_by,create_time,
        update_by,update_time
    </sql>

    <select id="getBlogPage" resultMap="BlogArticleMap">
        SELECT ba.id,
        ba.create_time,
        ba.update_time,
        ba.blog_title,
        ba.blog_sub_title,
        ba.category,
        ba.creator_code,
        ba.publish_date,
        ba.cover_save_path,
        ba.cover_show_url,
        ba.view_count,
        ba.language,
        ba.blog_status,
        ba.domain
        FROM blog_article ba
        WHERE
        ba.blog_status &lt;&gt; 'Deleted'
        <if test="query.title != null ">
            AND ba.blog_title LIKE CONCAT('%', #{query.title}, '%')
        </if>
        <if test="query.beginTime != null and query.endTime != null ">
            AND ba.create_time BETWEEN #{query.beginTime} AND #{query.endTime}
        </if>
        <if test="query.status != null ">
            AND ba.blog_status = #{query.status}
        </if>
        <if test="query.category != null ">
            AND ba.category = #{query.category}
        </if>
        <if test="query.language != null">
            AND ba.language = #{query.language}
        </if>
        <if test="query.domain != null">
            AND ba.domain = #{query.domain}
        </if>
        <if test="query.isMarketplace == true">
            <if test="query.categoryList != null and query.categoryList.size > 0">
                AND ba.category IN
                <foreach item="item" index="index" collection="query.categoryList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>
</mapper>
