<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BillMapper">

    <select id="queryLacksAbstract" resultType="com.zsmall.system.entity.domain.Bill">
        SELECT bl.*
        FROM bill bl
        WHERE NOT EXISTS(
        SELECT 1
        FROM bill_abstract ba
        JOIN bill_abstract_detail bad ON ba.id = bad.bill_abstract_id
        WHERE ba.bill_id = bl.id
        AND bad.field_type IN ('Total_ActivityPenaltyFee', 'Total_ActivityStorageFee')
        AND bad.del_flag = 0)
        <if test="billNo != null">
            AND bl.bill_no = #{billNo}
        </if>
        AND EXISTS(
        SELECT 1
        FROM bill_relation br
        WHERE br.bill_id = bl.id
        AND br.relation_type IN ('ActivityPenaltyFee', 'ActivityStorageFee') AND br.del_flag = 0
        )
        AND bl.del_flag = 0
    </select>

    <select id="queryPage" resultType="com.zsmall.system.entity.domain.Bill">
        SELECT b.* FROM bill b WHERE 1=1
        <if test="queryValue != null">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'BillNo')">
                AND b.bill_no LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'UserCode')">
                AND b.tenant_id = #{queryValue}
            </if>
        </if>
        <if test="begin != null and end != null">
            AND b.settlement_cycle_begin BETWEEN #{begin} AND #{end}
        </if>
        AND b.del_flag = 0
        ORDER BY b.create_time DESC, b.id DESC
    </select>

    <select id="sumCircularDeposit" resultType="java.math.BigDecimal">
        SELECT SUM(b.current_circular_deposit)
        FROM bill b
        WHERE
        b.bill_state = 0
        <if test="tenantId != null">
            AND b.tenant_id = #{tenantId}
        </if>
        AND b.del_flag = 0
    </select>

    <select id="sumCurrentTotalAmountByBillNoList" resultType="java.math.BigDecimal">
        SELECT SUM(b.current_total_amount)
        FROM bill b
        WHERE b.del_flag = 0
        AND b.bill_no IN
        <foreach collection="billNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="sumUnsettledTotalAmount" resultType="java.math.BigDecimal">
        SELECT SUM(b.current_total_amount)
        FROM bill b
        WHERE
        b.bill_state = 1 AND b.withdrawal_state = 0
        <if test="tenantId != null">
            AND b.tenant_id = #{tenantId}
        </if>
        AND b.del_flag = 0
    </select>


    <select id="getAllBillRelation" resultType="com.zsmall.system.entity.domain.BillRelation">
        SELECT br.*
        FROM bill b
                 INNER JOIN bill_relation br on b.id = br.bill_id
        WHERE br.del_flag = '0'
        <if test="startDate != null and startDate != ''">
            and b.create_time &gt;= #{startDate,jdbcType=VARCHAR}
        </if>
        <if test="endDate != null and endDate != ''">
            and b.create_time &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getAllBillRelationDetailsByBillIdSet" resultType="java.lang.String">
        SELECT
        GROUP_CONCAT( brd.field_type, ':', brd.field_value ) AS concatenated_values
        FROM
        bill_relation_detail  brd
        WHERE
        bill_relation_id IN ( SELECT br.id FROM bill_relation br WHERE bill_id  in
        <if test="billIdSet != null and billIdSet.size() != 0">
            <foreach collection="billIdSet" item="item" index="index" open="(" close=")" separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        )
        GROUP BY bill_relation_id;
    </select>

    <select id="getBillRelationDetailByBillIDSet" resultType="com.zsmall.system.entity.domain.Bill">
        SELECT
        br.bill_id as previousBillId,
            br.belong_abstract_type as billNo,
            GROUP_CONCAT(brd.field_type, ':', brd.field_value) AS billCycleNo
        FROM
            bill_relation_detail brd
                JOIN
            bill_relation br
            ON
                brd.bill_relation_id = br.id
        WHERE
            br.bill_id in
        <if test="billIdSet != null and billIdSet.size() != 0">
            <foreach collection="billIdSet" item="item" index="index" open="(" close=")" separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY
            brd.bill_relation_id;

    </select>
</mapper>
