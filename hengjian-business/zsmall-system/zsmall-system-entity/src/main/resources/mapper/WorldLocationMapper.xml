<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.WorldLocationMapper">

    <resultMap id="ForSelectResultMap" type="com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationSelectVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="locationName" column="location_name" jdbcType="VARCHAR"/>
        <result property="locationOtherName" column="location_other_name" jdbcType="OTHER"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="queryListForSelect"
            resultMap="ForSelectResultMap"
            resultType="com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationSelectVo">
        SELECT
            wl.id,
            wl.location_name,
            wl.location_other_name
        FROM world_location wl
        WHERE 1=1
        <if test="bo.parentId != null">
            AND wl.parent_id = #{bo.parentId}
        </if>
        <if test="bo.locationType != null">
            AND wl.location_type = #{bo.locationType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.parentCode)">
            AND EXISTS(SELECT 1 FROM world_location parent WHERE parent.id = wl.parent_id AND parent.location_code = #{bo.parentCode})
        </if>
        ORDER BY JSON_UNQUOTE(wl.location_other_name -> '$.en_US')
    </select>

    <select id="queryChildCodeList" resultType="java.lang.String">
        SELECT cwl.location_code
        FROM world_location cwl
                 JOIN world_location pwl ON cwl.parent_id = pwl.id
        WHERE pwl.location_code = #{countryCode}
    </select>
    <select id="getByLocationOthName" resultType="com.zsmall.system.entity.domain.WorldLocation">
        SELECT
            *
        FROM world_location
        WHERE location_other_name like CONCAT(CONCAT('%', #{state}), '%')
    </select>
</mapper>
