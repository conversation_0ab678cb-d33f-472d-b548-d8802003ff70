<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.UserFeedbackMapper">
  <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.UserFeedback">
    <!--@mbg.generated-->
    <!--@Table user_feedback-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="tenant_type" jdbcType="VARCHAR" property="tenantType" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="feedback_title" jdbcType="VARCHAR" property="feedbackTitle" />
    <result column="feedback_type" jdbcType="VARCHAR" property="feedbackType" />
    <result column="feedback_content" jdbcType="LONGVARCHAR" property="feedbackContent" />
    <result column="reply_content" jdbcType="LONGVARCHAR" property="replyContent" />
    <result column="reply_state" jdbcType="VARCHAR" property="replyState" />
    <result column="reply_time" jdbcType="TIMESTAMP" property="replyTime" />
    <result column="replier_id" jdbcType="VARCHAR" property="replierId" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, tenant_type, email, feedback_title, feedback_type, feedback_content,
    reply_content, reply_state, reply_time, replier_id, del_flag, create_by, create_time,
    update_by, update_time
  </sql>

  <select id="queryPage" resultType="com.zsmall.system.entity.domain.UserFeedback">
      SELECT uf.*
      FROM user_feedback uf
      WHERE uf.del_flag = '0'
      <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.queryValue)">
          AND uf.tenant_id LIKE CONCAT('%', #{bo.queryValue}, '%')
      </if>
      <if test="bo.feedbackDate != null">
          AND DATE_FORMAT(uf.create_time, '%Y-%m-%d') = #{bo.feedbackDate}
      </if>
      <if test="bo.feedbackType != null">
          AND uf.feedback_type = #{bo.feedbackType}
      </if>
      <if test="bo.replyState != null">
          AND uf.reply_state = #{bo.replyState}
      </if>
      ORDER BY uf.create_time DESC
  </select>
</mapper>
