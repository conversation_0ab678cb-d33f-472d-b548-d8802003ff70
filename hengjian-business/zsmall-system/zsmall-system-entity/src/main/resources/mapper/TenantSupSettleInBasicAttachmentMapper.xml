<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantSupSettleInBasicAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.TenantSupSettleInBasicAttachment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ossId" column="oss_id" jdbcType="BIGINT"/>
            <result property="attachmentName" column="attachment_name" jdbcType="VARCHAR"/>
            <result property="attachmentOriginalName" column="attachment_original_name" jdbcType="VARCHAR"/>
            <result property="attachmentSuffix" column="attachment_suffix" jdbcType="VARCHAR"/>
            <result property="attachmentSavePath" column="attachment_save_path" jdbcType="VARCHAR"/>
            <result property="attachmentShowUrl" column="attachment_show_url" jdbcType="VARCHAR"/>
            <result property="attachmentSort" column="attachment_sort" jdbcType="INTEGER"/>
            <result property="attachmentType" column="attachment_type" jdbcType="VARCHAR"/>
            <result property="fileNature" column="file_nature" jdbcType="INTEGER"/>
            <result property="basicId" column="basic_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,oss_id,
        attachment_name,attachment_original_name,attachment_suffix,
        attachment_save_path,attachment_show_url,attachment_sort,
        attachment_type,file_nature,basic_id,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
