<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantReceiptAccountCreditMapper">

    <select id="selectReceiptAccountCredit"
            resultType="com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountCreditVo">
        select * from
            tenant_receipt_account_credit
        ${ew.getCustomSqlSegment}
    </select>
</mapper>
