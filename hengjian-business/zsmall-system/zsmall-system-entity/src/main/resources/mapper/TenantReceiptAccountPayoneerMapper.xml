<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantReceiptAccountPayoneerMapper">

    <select id="selectReceiptAccountPayoneer"
            resultType="com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountPayoneerVo">
        select * from
            tenant_receipt_account_payoneer
        ${ew.getCustomSqlSegment}
    </select>
</mapper>
