<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantSupSettleInBasicMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.TenantSupSettleInBasic">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="socialCreditCode" column="social_credit_code" jdbcType="VARCHAR"/>
            <result property="countryId" column="country_id" jdbcType="BIGINT"/>
            <result property="stateId" column="state_id" jdbcType="BIGINT"/>
            <result property="stateText" column="state_text" jdbcType="VARCHAR"/>
            <result property="cityText" column="city_text" jdbcType="VARCHAR"/>
            <result property="registeredAddress" column="registered_address" jdbcType="VARCHAR"/>
            <result property="legalPersonPlace" column="legal_person_place" jdbcType="VARCHAR"/>
            <result property="documentType" column="document_type" jdbcType="INTEGER"/>
            <result property="documentNumber" column="document_number" jdbcType="VARCHAR"/>
            <result property="legalPersonName" column="legal_person_name" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,company_name,
        social_credit_code,country_id,state_id,
        state_text,city_text,registered_address,
        legal_person_place,document_type,document_number,
        legal_person_name,create_by,create_time,
        update_by,update_time,del_flag
    </sql>
</mapper>
