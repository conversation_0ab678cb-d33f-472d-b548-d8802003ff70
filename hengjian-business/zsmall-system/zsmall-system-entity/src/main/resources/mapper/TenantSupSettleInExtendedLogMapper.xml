<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantSupSettleInExtendedLogMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.TenantSupSettleInExtendedLog">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="recordId" column="record_id" jdbcType="BIGINT"/>
            <result property="extendedId" column="extended_id" jdbcType="BIGINT"/>
            <result property="companyNature" column="company_nature" jdbcType="INTEGER"/>
            <result property="mainCategories" column="main_categories" jdbcType="VARCHAR"/>
            <result property="isOnlineRetailersExperience" column="is_online_retailers_experience" jdbcType="BIT"/>
            <result property="mainProductionCountryId" column="main_production_country_id" jdbcType="BIGINT"/>
            <result property="isOverseasWarehouse" column="is_overseas_warehouse" jdbcType="BIT"/>
            <result property="countryId" column="country_id" jdbcType="BIGINT"/>
            <result property="stateId" column="state_id" jdbcType="BIGINT"/>
            <result property="stateText" column="state_text" jdbcType="VARCHAR"/>
            <result property="cityText" column="city_text" jdbcType="VARCHAR"/>
            <result property="warehouseAddress" column="warehouse_address" jdbcType="VARCHAR"/>
            <result property="otherChannels" column="other_channels" jdbcType="VARCHAR"/>
            <result property="otherChannelText" column="other_channel_text" jdbcType="VARCHAR"/>
            <result property="deliverTimeLimit" column="deliver_time_limit" jdbcType="VARCHAR"/>
            <result property="salesPriceRecentYear" column="sales_price_recent_year" jdbcType="DECIMAL"/>
            <result property="basicId" column="basic_id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_id,extended_id,
        company_nature,main_categories,is_online_retailers_experience,
        main_production_country_id,is_overseas_warehouse,country_id,
        state_id,state_text,city_text,
        warehouse_address,other_channels,other_channel_text,
        deliver_time_limit,sales_price_recent_year,basic_id,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>
</mapper>
