<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantWalletMapper">

    <select id="sumDistributorBalance" resultType="java.math.BigDecimal">
        select
            CAST(SUM(tw.wallet_balance)AS decimal(20,2)) as total
        from tenant_wallet tw, sys_tenant t
        where tw.tenant_id = t.tenant_id and t.tenant_type = 'Distributor'
    </select>

    <select id="sumDistributorBalanceByCurrency" resultType="java.math.BigDecimal">
        select
            CAST(SUM(tw.wallet_balance)AS decimal(20,2)) as total
        from tenant_wallet tw, sys_tenant t
        where tw.tenant_id = t.tenant_id and t.tenant_type = 'Distributor'
        and tw.currency = #{currency}
    </select>
</mapper>
