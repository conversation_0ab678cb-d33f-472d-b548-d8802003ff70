<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper">

    <select id="getCurrencyList" resultType="com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo">
        select id,currency_code,currency_symbol,country_code
        from site_country_currency
        where del_flag = 0
        group by currency_code,currency_symbol
    </select>

    <select id="getSiteCurrencyList" resultType="com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo">
        select id, country_code,country_name,currency_code,currency_symbol
        from site_country_currency
        where del_flag = 0
        group by country_code,country_name,currency_code,currency_symbol
    </select>

    <select id="getCurrencySymbolByCurrencyCode" resultType="java.lang.String">
        select currency_symbol
        from site_country_currency
        where currency_code = #{currencyCode}
        and del_flag = 0
        limit 1;
    </select>
</mapper>
