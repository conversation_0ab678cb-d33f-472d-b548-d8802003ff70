<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BillRelationMapper">

    <select id="queryRelationTypeListByBillId" resultType="java.lang.String">
        SELECT br.relation_type
        FROM bill_relation br
        WHERE br.bill_id = #{billId}
          AND br.del_flag = 0
        GROUP BY br.relation_type
    </select>

    <select id="queryRelationTypeTotalAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(cast(brd.field_value as DECIMAL(10, 2))), 0)
        FROM bill_relation br
                 JOIN bill_relation_detail brd ON br.id = brd.bill_relation_id
        WHERE br.bill_id = #{billId}
          AND br.relation_type = #{relationType}
          AND brd.field_type = #{fieldType}
          AND br.del_flag = 0
          AND brd.del_flag = 0
    </select>

    <select id="queryActivityOrderRelation" resultType="com.zsmall.system.entity.domain.BillRelation">
        SELECT DISTINCT br.*
        FROM bill_relation br
                 JOIN bill_relation_detail brd ON br.id = brd.bill_relation_id
        WHERE br.relation_type IN ('OrderItem', 'OrderRefundItem')
          AND brd.field_type = 'OrderNo'
          AND EXISTS(
                SELECT 1
                FROM order_item oi JOIN orders o ON oi.order_id = o.id
                WHERE o.order_no = brd.field_value
                  AND oi.activity_code IS NOT NULL AND oi.activity_code != ''
            )
    </select>
</mapper>
