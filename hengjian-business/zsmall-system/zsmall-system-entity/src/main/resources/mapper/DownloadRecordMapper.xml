<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.DownloadRecordMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.DownloadRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="expiredTime" column="expired_time" jdbcType="TIMESTAMP"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="fileSize" column="file_size" jdbcType="VARCHAR"/>
            <result property="fileSaveKey" column="file_save_key" jdbcType="VARCHAR"/>
            <result property="downloadQuery" column="download_query" jdbcType="VARCHAR"/>
            <result property="downloadType" column="download_type" jdbcType="VARCHAR"/>
            <result property="recordStatus" column="record_status" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="PageResultMap" type="com.zsmall.system.entity.domain.vo.DownloadRecordVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="businessImportExportId" column="business_import_export_id" jdbcType="BIGINT"/>
        <result property="ossId" column="oss_id" jdbcType="BIGINT"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileSize" column="file_size" jdbcType="VARCHAR"/>
        <result property="fileSaveKey" column="file_save_key" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="downloadQuery" column="download_query" jdbcType="VARCHAR"/>
        <result property="downloadType" column="download_type" jdbcType="VARCHAR"/>
        <result property="expiredTime" column="expired_time" jdbcType="TIMESTAMP"/>
        <result property="recordState" column="record_state" jdbcType="VARCHAR"/>
        <result property="importMessage" column="import_message" jdbcType="OTHER" typeHandler="com.zsmall.common.handler.HuToolJsonTypeHandler"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,expired_time,file_name,
        file_size,file_save_key,download_query,
        download_type,record_status,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="pageDownloadRecordVoList" resultMap="PageResultMap">
        select id,business_import_export_id,oss_id,tenant_id,file_name,file_size,file_save_key,file_url,download_query,
               download_type,expired_time,record_state,import_message,create_time
        from download_record
        <where>
            del_flag = 0
            <if test="null != tenantId">
                and tenant_id = #{tenantId}
            </if>
        order by create_time desc
        </where>
    </select>

    <select id="countDownloadRecordVoList" resultType="java.lang.Integer">
        select count(1)
        from download_record
        <where>
            del_flag = 0
            <if test="null != tenantId">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>
</mapper>
