<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TransactionsOrdersMapper">
    <select id="getChannelName" resultType="map">
        select  too.transactions_id,tsc.channel_name
        from  transactions_orders too
        inner join orders o on too.order_id=o.id
        inner join tenant_sales_channel tsc on tsc.id=o.channel_id
        where 1=1
        <if test="transactionNoSet != null and transactionNoSet.size() != 0">
            and too.transactions_id in
            <foreach collection="transactionNoSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
