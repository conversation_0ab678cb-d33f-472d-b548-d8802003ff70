<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantSalesChannelMapper">
    <select id="queryChannelIdAndAlias" resultType="java.lang.Object">
        SELECT tsc.id, tsc.channel_alias
        FROM tenant_sales_channel tsc
        WHERE tsc.del_flag = '0'
          AND tsc.tenant_id = #{tenantId}
          AND tsc.channel_type = #{channelType}
    </select>
    <select id="selectAccountListRefactor" resultType="com.zsmall.system.entity.domain.TenantSalesChannel">
        select id,connect_str from   tenant_sales_channel
        <where>
            and del_flag != 2

            <if test="id != null">
                and id = #{id}
            </if>
            <if test="channelType != null and channelType !='' ">
                and channel_type = #{channelType}
            </if>
        </where>
    </select>
</mapper>
