<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TransactionRecordAttachmentMapper">

    <insert id="insertBatch">
        INSERT INTO transaction_record_attachment (
            transaction_no,
            attachment_name,
            attachment_original_name,
            attachment_suffix,
            attachment_save_path,
            attachment_show_url,
            attachment_sort,
            attachment_type,
            create_by,
            create_time,
            update_by,
            update_time,
            oss_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.transactionNo},
                #{item.attachmentName},
                #{item.attachmentOriginalName},
                #{item.attachmentSuffix},
                #{item.attachmentSavePath},
                #{item.attachmentShowUrl},
                #{item.attachmentSort},
                #{item.attachmentType},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.ossId}
            )
        </foreach>
    </insert>

    <select id="getByTransactionRecordId"
            resultType="com.zsmall.system.entity.domain.TransactionRecordAttachment">
        select tra.*,tr.id as ossId
        from transaction_record tr
                 inner join transaction_receipt trr on tr.id = trr.transactions_id
                 inner join transaction_receipt_attachment tra on trr.transaction_receipt_no = tra.transaction_receipt_no
        where 1=1
        <if test="collection != null and collection.size() != 0">
            and tr.id in
            <foreach collection="transactionRecordIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAttachmentByTransactionRecordId"
            resultType="com.zsmall.system.entity.domain.TransactionRecordAttachment">
        select tra.*,tr.id as ossId
        from transaction_record_attachment tra  inner join transaction_record tr on
            tra.transaction_no=tr.transaction_no
    </select>
</mapper>
