package com.zsmall.xxl.job.factory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.amazon.client.model.Address;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.FulfillmentPushStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderAddressType;
import com.zsmall.common.enums.order.OrderType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrder;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderAddress;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderItem;
import com.zsmall.extend.shop.amazon.job.support.AmznOrderSupport;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import com.zsmall.order.entity.domain.event.GenerateOrderItemEvent;
import com.zsmall.order.entity.domain.event.RecalculateOrderAmountEvent;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.IShopifyExtraPropertiesService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.xxl.job.factory.ThirdChannelFactory;
import com.zsmall.xxl.job.factory.ThirdChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Amazon相关业务实现
 *
 * <AUTHOR>
 * @date 2023/11/1
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AmazonImpl implements ThirdChannelService {

    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductCategoryService iProductCategoryService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IShopifyExtraPropertiesService iShopifyExtraPropertiesService;
    private final IProductActivityItemService iProductActivityItemService;

    private final IProductMappingService iProductMappingService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    private final OrderSupport orderSupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        ThirdChannelFactory.register(ChannelTypeEnum.Amazon, this);
    }

    /**
     * 推送商品至渠道店铺
     *
     * @param mappingList
     */
    @Override
    public void pushProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 更新商品
     *
     * @param mappingList
     */
    @Override
    public void updateProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 取消同步商品
     *
     * @param mappingList
     */
    @Override
    public void cancelProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 删除商品
     *
     * @param mappingList
     */
    @Override
    public void deleteProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 从渠道店铺拉取订单
     *
     * @param startDate
     * @param endDate
     */
    @Override
    public void pullOrder(String startDate, String endDate) {
        log.info("Amazon定时任务【拉取订单】 - 起始时间 = {} 截止时间 = {}", startDate, endDate);
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.Amazon);
        log.info("Amazon定时任务【拉取订单】 - 有效店铺数量 = {}", CollUtil.size(channelList));

        DateTime startDateTime;
        DateTime endDateTime;
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            // 减去3天作为起始时间
            startDateTime = DateTime.now().offset(DateField.DAY_OF_MONTH, -3);
            endDateTime = DateTime.now();
        } else {
            startDateTime = DateTime.of(startDate, "yyyy-MM-dd HH:mm:ss");
            endDateTime = DateTime.of(endDate, "yyyy-MM-dd HH:mm:ss");
        }

        for (TenantSalesChannel salesChannel : channelList) {
            String tenantId = salesChannel.getTenantId();
            String channelName = salesChannel.getChannelName();
            try {

                List<AmznSyncOrder> channelOrderList = new ArrayList<>();

                Page<AmznSyncOrder> inPage = new Page<>(1, 20);
                Page<AmznSyncOrder> page = amznOrderSupport.queryAmznOrderByDateTime(inPage, channelName, startDateTime, endDateTime);
                List<AmznSyncOrder> records = page.getRecords();

                if (CollUtil.isNotEmpty(records)) {
                    channelOrderList.addAll(records);
                    while (page.hasNext()) {
                        page = amznOrderSupport.queryAmznOrderByDateTime(page.setCurrent(page.getCurrent() + 1), channelName, startDateTime, endDateTime);
                        List<AmznSyncOrder> newRecords = page.getRecords();
                        if (CollUtil.isNotEmpty(newRecords)) {
                            channelOrderList.addAll(newRecords);
                        }
                    }
                }

                List<Orders> ordersList = ordersBodyToEntity(channelOrderList, salesChannel);
                if (CollUtil.isNotEmpty(ordersList)) {
                    Boolean autoPayment = ZSMallSystemEventUtils.checkAutoPaymentEvent(tenantId);
                    if (autoPayment) {
                        try {
                            orderSupport.orderPayChain(tenantId, ordersList, true, true);
                        } catch (RStatusCodeException e) {
                            log.error("Amazon店铺[{}]，订单自动支付失败，原因 {}", channelName, e.getMessage(), e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Amazon店铺[{}]，拉取订单失败，原因 {}", channelName, e.getMessage(), e);
            }
        }
    }

    /**
     * 推送履约信息至渠道店铺
     *
     * @param fulfillmentRecordList
     */
    @Override
    public void pushFulfillment(List<ThirdChannelFulfillmentRecord> fulfillmentRecordList) {
        log.info("Amazon定时任务【推送履约信息至渠道店铺】 需要推送的数量 = {}", CollUtil.size(fulfillmentRecordList));
        for (ThirdChannelFulfillmentRecord fulfillmentRecord : fulfillmentRecordList) {

            String orderNo = fulfillmentRecord.getOrderNo();
            Long channelId = fulfillmentRecord.getChannelId();
            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);

            try {
                if (salesChannel != null) {




                }
            } catch (RStatusCodeException e) {
                log.info("Amazon订单[{}]，推送履约信息至渠道店铺出现业务异常，原因 {}", orderNo, e.getMessage(), e);
                fulfillmentRecord.setChannelFulfillmentMessage(LocaleMessage.byStatusCodeToJSON(e.getStatusCode()));
                fulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.PushFailed);
            } catch (Exception e) {
                log.info("Amazon订单[{}]，推送履约信息至渠道店铺出现未知异常，原因 {}", orderNo, e.getMessage(), e);
                fulfillmentRecord.setChannelFulfillmentMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.PUSH_FULFILLMENT_TO_SALES_CHANNEL_ERROR));
                fulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.PushFailed);
            }
            iThirdChannelFulfillmentRecordService.updateById(fulfillmentRecord);
        }
    }

    /**
     * 更新库存
     *
     * @param mappingList
     * @param stockTotal
     */
    @Override
    public void updateStock(List<ProductMapping> mappingList, Integer stockTotal) {

    }

    @Override
    public void pullOrderByJson(String voJson, String startDate, String endDate) {

    }

    @Override
    public void pullProduct(String json) {

    }

    private final AmznOrderSupport amznOrderSupport;

    /**
     * 将Orders JSON响应信息转化为实体类
     *
     * @param channelOrderList
     * @param channel
     * @return
     */
    private List<Orders> ordersBodyToEntity(List<AmznSyncOrder> channelOrderList, TenantSalesChannel channel) throws RStatusCodeException {
        List<Orders> orderList = new ArrayList<>();
        Long channelId = channel.getId();
        String tenantId = channel.getTenantId();

        for (AmznSyncOrder channelOrder : channelOrderList) {
            Date createTime = channelOrder.getCreateTime();
            String channelOrderNo = channelOrder.getAmazonOrderId();
            String channelOrderName = channelOrder.getAmazonOrderId();

            Orders order = iOrdersService.queryValidOrder(channelId, tenantId, channelOrderNo.toString());
            if (order == null) {  // 数据库中没有存过该订单，准备创建实体类
                order = new Orders();
                String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
                order.setOrderNo(orderNo);
                order.setTenantId(tenantId);
                order.setOrderType(OrderType.Normal);
                order.setChannelType(ChannelTypeEnum.Amazon);
                order.setLogisticsType(LogisticsTypeEnum.DropShipping);
                order.setChannelId(channelId);
                order.setChannelOrderNo(channelOrderNo);
                order.setChannelOrderName(channelOrderName);
                order.setChannelOrderTime(createTime);

                // 物流信息
                OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
                // Shopify目前都是代发
                // orderLogisticsInfo.setOrderId(orderId);
                orderLogisticsInfo.setOrderNo(orderNo);
                orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.DropShipping);

                // 收货地址
                OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
                List<AmznSyncOrderAddress> amznSyncOrderAddresses = amznOrderSupport.queryOrderAddressByAmazonOrderId(channelOrderNo);
                if (CollUtil.isNotEmpty(amznSyncOrderAddresses)) {
                    AmznSyncOrderAddress amznSyncOrderAddress = amznSyncOrderAddresses.get(0);
                    Address shippingAddress = amznSyncOrderAddress.getShippingAddress();

                    String zip = shippingAddress.getPostalCode();
                    String countryCode = shippingAddress.getCountryCode();

                    orderAddressInfo.setOrderNo(orderNo);
                    orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
                    orderAddressInfo.setRecipient(shippingAddress.getName());
                    orderAddressInfo.setPhoneNumber(shippingAddress.getPhone());
                    orderAddressInfo.setCountry(countryCode);
                    orderAddressInfo.setCountryCode(countryCode);
                    orderAddressInfo.setState(shippingAddress.getStateOrRegion());
                    orderAddressInfo.setStateCode(shippingAddress.getStateOrRegion());
                    orderAddressInfo.setCity(shippingAddress.getCity());
                    orderAddressInfo.setAddress1(shippingAddress.getAddressLine1());
                    orderAddressInfo.setAddress2(shippingAddress.getAddressLine2());
                    orderAddressInfo.setZipCode(zip);

                    orderLogisticsInfo.setZipCode(StrUtil.trim(zip));
                    orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(zip));

                    if (StrUtil.contains(zip, "-")) {
                        // 存在-的邮编，需要分割出前面5位的主邮编
                        String mainZipCode = StrUtil.trim(StrUtil.split(zip, "-").get(0));
                        orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
                    }
                    orderLogisticsInfo.setLogisticsCountryCode(countryCode);
                }


                List<AmznSyncOrderItem> amznSyncOrderItems = amznOrderSupport.queryOrderItemByAmazonOrderId(channelOrderNo);
                List<OrderItem> orderItemList = new ArrayList<>();
                List<OrderItemPrice> orderItemPriceList = new ArrayList<>();

                LocaleMessage localeMessage = new LocaleMessage();
                Integer totalQuantity = 0;

                if (CollUtil.isEmpty(amznSyncOrderItems)) {
                    // 没有子订单、跳过当前订单
                    continue;
                } else {
                    for (AmznSyncOrderItem channelOrderItem : amznSyncOrderItems) {
                        Long itemId = channelOrderItem.getId();
                        String amazonOrderItemId = channelOrderItem.getOrderItemId();
                        String mappingSku = channelOrderItem.getSellerSku();
                        Integer quantity = channelOrderItem.getQuantityOrdered();
                        totalQuantity += quantity;

                        ProductMapping productMapping = iProductMappingService.queryByTenantAndMappingSku(tenantId, channelId, mappingSku);
                        if (productMapping != null) {
                            GenerateOrderItemEvent generateOrderItemEvent = new GenerateOrderItemEvent();
                            generateOrderItemEvent.setDTenantId(tenantId);
                            generateOrderItemEvent.setOrder(order);
                            generateOrderItemEvent.setChannelTypeEnum(ChannelTypeEnum.Amazon);
                            generateOrderItemEvent.setLogisticsType(LogisticsTypeEnum.DropShipping);
                            generateOrderItemEvent.setCountry(orderAddressInfo.getCountryCode());
                            generateOrderItemEvent.setActivityCode(productMapping.getActivityCode());
                            generateOrderItemEvent.setProductSkuCode(productMapping.getProductSkuCode());
                            generateOrderItemEvent.setTotalQuantity(quantity);
                            SpringUtils.context().publishEvent(generateOrderItemEvent);

                            OrderItemDTO outDTO = generateOrderItemEvent.getOutDTO();
                            LocaleMessage message = outDTO.getLocaleMessage();
                            if (message.hasData()) {
                                localeMessage.append(message);
                            }

                            OrderItem orderItem = outDTO.getOrderItem();
                            OrderItemPrice orderItemPrice = outDTO.getOrderItemPrice();
                            OrderItemProductSku orderItemProductSku = outDTO.getOrderItemProductSku();

                            orderItem.setChannelItemNo(StrUtil.toStringOrNull(itemId));
                            orderItem.setTenantId(tenantId);
                            orderItemProductSku.setTenantId(tenantId);

                            orderItem.setOrderItemPrice(orderItemPrice);
                            orderItem.setOrderItemProductSku(orderItemProductSku);

                            orderItemList.add(orderItem);
                            orderItemPriceList.add(orderItemPrice);
                        } else {
                            log.info("Amazon订单 {} 不存在映射SKU {}", channelOrderNo, mappingSku);
                        }
                    }
                }


                log.info("Amazon订单 {} 有效的子订单数量 {}", channelOrderNo, CollUtil.size(orderItemList));
                if (CollUtil.isNotEmpty(orderItemList)) {
                    order.setTotalQuantity(totalQuantity);

                    RecalculateOrderAmountEvent recalculateOrderAmountEvent = new RecalculateOrderAmountEvent();
                    recalculateOrderAmountEvent.setOrder(order);
                    recalculateOrderAmountEvent.setOrderItemPriceList(orderItemPriceList);
                    SpringUtils.publishEvent(recalculateOrderAmountEvent);

                    iOrdersService.save(order);
                    Long orderId = order.getId();

                    iOrderLogisticsInfoService.save(orderLogisticsInfo.setOrderId(orderId));

                    if (orderAddressInfo.getOrderNo() != null) {
                        iOrderAddressInfoService.save(orderAddressInfo.setOrderId(orderId));
                    }

                    for (OrderItem orderItem : orderItemList) {
                        iOrderItemService.save(orderItem.setOrderId(orderId));
                        Long orderItemId = orderItem.getId();

                        OrderItemPrice orderItemPrice = orderItem.getOrderItemPrice();
                        OrderItemProductSku orderItemProductSku = orderItem.getOrderItemProductSku();

                        iOrderItemPriceService.save(orderItemPrice.setOrderItemId(orderItemId));
                        iOrderItemProductSkuService.save(orderItemProductSku.setOrderItemId(orderItemId));
                    }
                    orderList.add(order);
                }
            }
        }
        return orderList;
    }

}
