package com.zsmall.common.handler;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2025/04/02
 */
@MappedTypes(JSONObject.class)  // 声明处理的 Java 类型
@MappedJdbcTypes(JdbcType.VARCHAR)  // 声明对应的 JDBC 类型
public class HuToolJsonTypeHandler extends BaseTypeHandler<JSONObject> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, JSONObject parameter, JdbcType jdbcType) throws SQLException {
        // Hutool 的 JSONObject 转字符串写入数据库
        ps.setString(i, parameter.toString());
    }

    @Override
    public JSONObject getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonStr = rs.getString(columnName);
        return parseJson(jsonStr);
    }

    @Override
    public JSONObject getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonStr = rs.getString(columnIndex);
        return parseJson(jsonStr);
    }

    @Override
    public JSONObject getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonStr = cs.getString(columnIndex);
        return parseJson(jsonStr);
    }

    /**
     * 解析 JSON 字符串为 Hutool 的 JSONObject
     */
    private JSONObject parseJson(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return new JSONObject(); // 返回空对象，避免 null
        }
        return JSONUtil.parseObj(jsonStr);
    }
}
