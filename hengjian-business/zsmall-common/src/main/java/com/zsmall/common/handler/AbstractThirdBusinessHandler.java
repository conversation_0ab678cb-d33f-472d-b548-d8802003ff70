package com.zsmall.common.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.domain.base.ApiReturnMsg;
import com.zsmall.common.domain.base.ExpressSheet;

import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * lty notes 此抽象模版负责三方业务数据的转换处理,并流转到分销业务,但实际不进行数据的存储
 *
 * <AUTHOR> Theo
 * @create 2024/2/3 14:21
 */
@Slf4j
public abstract class AbstractThirdBusinessHandler<T extends ExpressSheet,A extends ApiReturnMsg,C> {
    public abstract void insertBusinessData(TikTokRespBaseEntity data, JSONObject json);

    /**
     * 功能描述：数据分解
     *
     * @param data 数据
     * @param json json
     * @return {@link List }<{@link C }>
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract List<C> dataDismantling(TikTokRespBaseEntity data, JSONObject json);
    /**
     * 功能描述：插入业务数据 性能优化版v2
     *
     * @param data            数据
     * @param json            json
     * @param businessNos     订单号集合
     * @param channelTypeEnum
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void insertBusinessDataV2(List<C> data, JSONObject json, ConcurrentHashMap<String, ConcurrentHashMap<String, String>>businessNos, BusinessTypeMappingEnum mappingEnum,ChannelTypeEnum channelTypeEnum);

    /**
     * 功能描述：插入业务数据模板,此模版方法 后续支持noMapping 和 haveMapping 多场景,后续把原模型改成这个模板方法就行
     *
     * @param list            数据
     * @param json            json
     * @param channelTypeEnum
     * <AUTHOR>
     * @date 2024/07/02
     */
    public void insertBusinessDataTemplate(List<C> list, JSONObject json,
                                           BusinessTypeMappingEnum mappingEnum, ChannelTypeEnum channelTypeEnum){

        // 发号
        ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos = generateBusinessNo(list);
        // 存入
        insertBusinessDataV2(list,json,businessNos,mappingEnum, channelTypeEnum);
    };
    /**
     * 功能描述：产生业务单号集合
     *
     * @param data 数据
     * @return {@link ConcurrentHashMap }
     * <AUTHOR>
     * @date 2024/06/30
     */
    public abstract ConcurrentHashMap<String,ConcurrentHashMap<String,String>> generateBusinessNo(List<C> data);
    public abstract boolean isExists(TikTokRespBaseEntity targetData) ;

    /**
     * 功能描述：数据筛选
     *
     * @param data 数据
     * @param json json
     * @return {@link TikTokRespBaseEntity }
     * <AUTHOR>
     * @date 2024/02/28
     */
    public abstract TikTokRespBaseEntity dataScreening(TikTokRespBaseEntity data, JSONObject json);




    public abstract void expressSheet(T expressSheet, A apiReturnMsg,
                                      AtomicInteger atomicInteger, JSON json);


}
