package com.zsmall.common.exception;

import com.zsmall.common.enums.error.GlobalErrorCodeEnum;
import com.zsmall.common.enums.order.B2cOrderErrorEnum;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 17:12
 */
public class OrderException extends AppRuntimeException {

    private B2cOrderErrorEnum errorCode;
    private GlobalErrorCodeEnum globalErrorCodeEnum;
    private String errorMessage;
    private String suffix;

    public OrderException(String message) {
        super(message);
        this.errorMessage = message;
    }


    public OrderException(B2cOrderErrorEnum errCode, String msg) {
        super(msg);
        this.errorCode = errCode;
        this.errorMessage = msg;
    }

    public OrderException(GlobalErrorCodeEnum globalErrorCodeEnum, String msg) {
        super(msg);
        this.globalErrorCodeEnum = globalErrorCodeEnum;
        this.errorMessage = msg;
    }

    public OrderException(B2cOrderErrorEnum errCode,GlobalErrorCodeEnum globalErrorCodeEnum, String msg) {
        super(msg);
        this.errorCode = errCode;
        this.globalErrorCodeEnum = globalErrorCodeEnum;
        this.errorMessage = msg;
    }

    public OrderException(B2cOrderErrorEnum errCode,GlobalErrorCodeEnum globalErrorCodeEnum, String msg,String suffix) {
        super(msg);
        this.errorCode = errCode;
        this.globalErrorCodeEnum = globalErrorCodeEnum;
        this.errorMessage = msg;
        this.suffix = suffix;
    }


    @Override
    public synchronized Exception fillInStackTrace() {
        return null;
    }

    public B2cOrderErrorEnum getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(B2cOrderErrorEnum errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public GlobalErrorCodeEnum getGlobalErrorCodeEnum() {
        return globalErrorCodeEnum;
    }

    public void setGlobalErrorCodeEnum(GlobalErrorCodeEnum globalErrorCodeEnum) {
        this.globalErrorCodeEnum = globalErrorCodeEnum;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    @Override
    public String getMessage() {
        return getErrorMessage();
    }
}
