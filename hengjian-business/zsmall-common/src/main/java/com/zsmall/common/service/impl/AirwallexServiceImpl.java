package com.zsmall.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.domain.airwallex.AirwallexToken;
import com.zsmall.common.domain.airwallex.payment.intents.CreatePaymentIntentRequest;
import com.zsmall.common.domain.airwallex.payment.intents.CreatePaymentIntentResponse;
import com.zsmall.common.enums.airwallex.AirwallexEnums;
import com.zsmall.common.service.AirwallexService;
import com.zsmall.common.util.AirwallexApiCallUtils;
import com.zsmall.common.util.AirwallexUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024年3月5日  18:44
 * @description:
 */
@Service
public class AirwallexServiceImpl implements AirwallexService {

    @Autowired
    AirwallexUtil airwallexUtil;

    @Resource
    private AirwallexApiCallUtils airwallexApiCallUtils;

    @Override
    public CreatePaymentIntentResponse createPaymentIntent(CreatePaymentIntentRequest createPaymentIntentRequest) {
        AirwallexToken accessToken = airwallexUtil.getAccessToken();
        if(null != accessToken){
            String url = AirwallexEnums.CREATE_A_PAYMENT_INTENT.getUrl()+ AirwallexEnums.CREATE_A_PAYMENT_INTENT.getPath();
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("Authorization","Bearer " + accessToken.getToken());
            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(createPaymentIntentRequest);
            String result = airwallexApiCallUtils.postApi(headerMap, url, jsonObject.toJSONString());
            CreatePaymentIntentResponse createPaymentIntentResponse = JSON.parseObject(result, CreatePaymentIntentResponse.class);
            return createPaymentIntentResponse;
        }
        return new CreatePaymentIntentResponse();
    }
}
