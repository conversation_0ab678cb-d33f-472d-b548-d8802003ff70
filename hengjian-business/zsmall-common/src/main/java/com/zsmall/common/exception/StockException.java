package com.zsmall.common.exception;

import com.hengjian.common.core.domain.RStatusCodeBase;
import com.zsmall.common.domain.LocaleMessage;

/**
 * 库存相关异常
 * <AUTHOR>
 * @date 2023/6/14
 */
public class StockException extends Exception {

    private LocaleMessage localeMessage;

    public StockException() {
        super();
    }

    public StockException(RStatusCodeBase RStatusCodeBase) {
        super(RStatusCodeBase.getMessage());
        this.localeMessage = LocaleMessage.byStatusCode(RStatusCodeBase);
    }

    public StockException(String message) {
        super(message);
    }

    public StockException(String message, Throwable cause) {
        super(message, cause);
    }

    public StockException(Throwable cause) {
        super(cause);
    }

    protected StockException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    /**
     * 获取国际化消息实体
     * @return
     */
    public LocaleMessage getLocaleMessage() {
        return localeMessage;
    }

}
