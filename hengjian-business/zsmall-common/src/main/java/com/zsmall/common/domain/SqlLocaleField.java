package com.zsmall.common.domain;

import lombok.Getter;
import lombok.Setter;

import java.lang.reflect.Field;
import java.util.function.Function;

/**
 * Sql国际化字段
 *
 * <AUTHOR>
 * @date 2023/6/19
 */
@Getter
@Setter
public class SqlLocaleField {

    private String zh_CN;

    private String en_US;

    private String ja_JP;

    public static SqlLocaleField build(Function<String, String> function) {
        SqlLocaleField localeField = new SqlLocaleField();

        Field[] declaredFields = SqlLocaleField.class.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            String localeText = function.apply(declaredField.getName());
            try {
                declaredField.set(localeField, localeText);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return localeField;
    }

}
