package com.zsmall.common.domain.airwallex.payment.intents;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年3月5日  16:40
 * @description: Create a PaymentIntent 接口返回 response
 */
@Data
public class CreatePaymentIntentResponse {

    private BigDecimal amount;
    private String cancellationReason;
    private String cancelledAt;
    private BigDecimal capturedAmount;
    private String clientSecret;
    private String connectedAccountId;
    private String createdAt;
    private String currency;
    private Customer customer;
    private String customerId;
    private String descriptor;
    private String id;
    private String invoiceId;
    private LatestPaymentAttempt latestPaymentAttempt;
    private String merchantOrderId;
    private RequestMetadata metadata;
    private NextAction nextAction;
    private Order order;
    private String paymentConsentId;
    private String paymentLinkId;
    private RequestPaymentMethodOptions paymentMethodOptions;
    private String requestId;
    private RiskControlOptions riskControlOptions;
    private String status;
    private List<String> availablePaymentMethodTypes;
    private String updatedAt;
}
