package com.zsmall.common.domain;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-30 11:14:14
 * @description 调用ERP算价接口请求参数
 */
 @Data
public class DeliveryFeeByErpResponse {
     //请求ID
     private String requestId;
     //仓库编码
     private String orgWarehouseCode;
     //快递公司
    private String logisticCode;
    //ERP的sku编码
    private List<ProductItem> skuList;
    //渠道店铺名
    private String channelFlag;
    //尾程派送费
    private BigDecimal shippingFee;

    private String carrierCode;
    // 货币符号
    private String  currencyCode;
    private String currencySymbol;
    /**
     * 是分销计算结果
     */
    private boolean isDistributionCalculateResult;
    @Data
    public static class ProductItem {
        /**
         * 商品编码
         */
        @NotEmpty(message = "商品编码不能为空！")
        private String  erpSku;

        @NotNull(message = "商品数量不能为空")
        private Integer  quantity;
    }
}
