package com.zsmall.common.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.zsmall.common.enums.BusinessParameterType;

import java.math.BigDecimal;

/**
 * ZS-Mall通用业务参数服务
 * <AUTHOR>
 * @date 2023/4/27
 */
public interface BusinessParameterService {

    /**
     * 获得自定义值类型内容的参数。
     *
     * @param parameterType
     * @param typeClz
     * @return
     */
    <T> T getValueFromType(BusinessParameterType parameterType, Class<T> typeClz);

    /**
     * 获得Double值内容的参数。
     *
     * @param parameterType  {@link BusinessParameterType} 业务参数类型
     * @return {@link Double}
     */
    Double getValueFromDouble(BusinessParameterType parameterType);

    /**
     * 获得BigDecimal值内容的参数。
     *
     * @param parameterType  {@link BusinessParameterType} 业务参数类型
     * @return {@link Double}
     */
    BigDecimal getValueFromBigDecimal(BusinessParameterType parameterType);

    /**
     * 获得Integer值内容的参数。
     *
     * @param parameterType  {@link BusinessParameterType} 业务参数类型
     * @return {@link Integer}
     */
    Integer getValueFromInteger(BusinessParameterType parameterType);

    /**
     * 获得String值内容的参数。
     *
     * @param parameterType  {@link BusinessParameterType} 业务参数类型
     * @return {@link String}
     */
    String getValueFromString(BusinessParameterType parameterType);

    /**
     * 获取JSONArray内容的参数
     * @param parameterType {@link BusinessParameterType} 业务参数类型
     * @return {@link JSONArray}
     */
    JSONArray getValueFromJSONArray(BusinessParameterType parameterType);

    /**
     * 获取JSONObject内容的参数
     * @param parameterType {@link BusinessParameterType} 业务参数类型
     * @return {@link JSONArray}
     */
    JSONObject getValueFromJSONObject(BusinessParameterType parameterType);

}
