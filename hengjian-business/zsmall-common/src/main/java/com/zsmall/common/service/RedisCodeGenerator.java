package com.zsmall.common.service;

import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.service.AbstractCodeGenerator;
import com.hengjian.common.redis.utils.RedisUtils;
import org.redisson.api.RIdGenerator;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Set;

/**
 * Redis 码生成器
 */
public abstract class RedisCodeGenerator extends AbstractCodeGenerator {

    private final static String PREFIX_INCREASE = GlobalConstants.GLOBAL_REDIS_KEY + "DIGITINCREASE:";
    private final static String MID_SECOND = "SECOND:";
    private final static String MID_MINUTE = "MINUTE:";

    /**
     * 单位秒内，一位自增数
     *
     * @return
     */
    public long getSecondOneDigitIncrease() {
        return getSecondOneDigitIncrease(LocalTime.now(), "ALL");
    }

    /**
     * 单位秒内，一位自增数
     *
     * @return
     */
    public long getSecondOneDigitIncrease(LocalTime localTime, String type) {
        String nowTimeString = localTime.format(FORMATTER_TIME);
        RedissonClient client = RedisUtils.getClient();

        String key = PREFIX_INCREASE + MID_SECOND + type + ":" + nowTimeString;

        RIdGenerator idGenerator = client.getIdGenerator(key);
        boolean b = idGenerator.tryInit(1, 1);
        // 初始化，第一次创建，则设置过期时间
        if (b) {
            // 15秒超时，稍微延后
            idGenerator.expire(Duration.ofSeconds(90));
        }
        // 考虑并发删除
        synchronized (RedisCodeGenerator.class) {
            while (true) {
                long id = idGenerator.nextId();
                if (id == 9L) {
                    // 删除自增，下次再重新初始化
                    idGenerator.delete();
                }
                return id;
            }
        }
    }

    /**
     * 秒级 - 校验重复
     *
     * @param type
     * @param uniqueCode
     * @return
     */
    public boolean checkSecondDuplicate(LocalTime localTime, String type, String uniqueCode) {
        try {
            String nowTimeString = localTime.format(FORMATTER_TIME);
            String key = PREFIX_INCREASE + MID_SECOND + "UNIQUE:" + type + ":" + nowTimeString;

            boolean existsObject = RedisUtils.isExistsObject(key);
            Set<String> cacheSet = RedisUtils.getCacheSet(key);
            if(cacheSet.contains(uniqueCode)) {
                return true;
            }
            RedisUtils.setCacheSetValue(key, uniqueCode);
            if (!existsObject) {
                RedisUtils.expireCacheSet(key, Duration.ofSeconds(90));
                // boolean expire = RedisUtils.expireCacheSet(key, Duration.ofSeconds(15));
                // Console.log("key = {}， expire ? {}", key, expire);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 分钟级 - 校验重复
     * @param localTime
     * @param type
     * @param uniqueCode
     * @return
     */
    public boolean checkMinuteDuplicate(LocalTime localTime, String type, String uniqueCode) {
        try {
            String nowTimeString = localTime.truncatedTo(ChronoUnit.MINUTES).format(FORMATTER_TIME);
            String key = PREFIX_INCREASE + MID_MINUTE + "UNIQUE:" + type + ":" + nowTimeString;

            boolean existsObject = RedisUtils.isExistsObject(key);
            Set<String> cacheSet = RedisUtils.getCacheSet(key);
            if(cacheSet.contains(uniqueCode)) {
                return true;
            }
            RedisUtils.setCacheSetValue(key, uniqueCode);
            if (!existsObject) {
                RedisUtils.expireCacheSet(key, Duration.ofMinutes(1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

}
