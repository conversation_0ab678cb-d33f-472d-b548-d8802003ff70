package com.zsmall.common.domain.airwallex.payment.intents;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年3月5日  16:43
 * @description:
 */
@Data
public class LatestPaymentAttempt {

    private BigDecimal amount;
    private AuthenticationData authenticationData;
    private String authorizationCode;
    private BigDecimal capturedAmount;
    private String createdAt;
    private String currency;
    private LatestPaymentAttemptdccData dccData;
    private String failureCode;
    private String id;
    private String merchantAdviceCode;
    private String merchantOrderid;
    private String paymentConsentid;
    private String paymentIntentid;
    private PaymentMethod paymentMethod;
    private LatestPaymentAttemptPaymentMethodOptions paymentMethodOptions;
    private String providerOriginalResponseCode;
    private String providerTransactionid;
    private BigDecimal refundedAmount;
    private String settleVia;
    private String status;
    private String updatedAt;
}
