package com.zsmall.common.enums.storageFee;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年7月4日  14:34
 * @description:
 */
@Getter
public enum PayStateEnum {

    /**
     * 待确认
     */
    UNPAID(0, "未支付"),
    /**
     * 确认中
     */
    PAID(1, "已支付");
    private final Integer value;

    private final String name;

    PayStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据value获取枚举
     */
    public static PayStateEnum getByValue(Integer value) {
        for (PayStateEnum item : values()) {
            if (item.value == value) {
                return item;
            }
        }
        return null;
    }
}
