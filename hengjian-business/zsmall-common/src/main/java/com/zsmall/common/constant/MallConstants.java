package com.zsmall.common.constant;

/**
 * 商城静态常量
 */
public interface MallConstants {

    /**
     * 商城名称
     */
    String MALL_NAME = "ZSMall";
    /**
     * 商城钱包
     */
    String MALL_WALLET_NAME = "ZSMall Wallet";
    /**
     * 商城系统内部
     * - 与钱包搭配，可理解成系统内部金额流转
     */
    String MALL_SYSTEM_NAME = "ZSMall System";


    /**
     * 配置表-对于前缀
     */
    String PREFIX_CONFIG = "CONFIG_";

    /**
     * 配置表
     */
    interface Config {
        /**
         * 隐私政策 - 带国际化 关键字
         */
        String Lang_PrivacyPolicy = "PrivacyPolicy{lang}";
        /**
         * FAQ - 带国际化 关键字
         */
        String Lang_FAQ = "FAQ{lang}";
        /**
         * 加入我们 - 关键字
         */
        String JoinUs = "JoinUs";

        /**
         * 普通商品链接 - 关键字
         */
        String NORMAL_PRODUCT_LINK = "NORMAL_PRODUCT_LINK";

        /**
         * 批发商品链接 - 关键字
         */
        String WHOLESALE_PRODUCT_LINK = "WHOLESALE_PRODUCT_LINK";
    }

    /**
     * 分销商设置
     */
    interface DistributorSettings {
        /**
         * 是否开启支付密码
         */
        String EnablePaymentPassword = "EnablePaymentPassword";
        /**
         * 支付密码
         */
        String PaymentPassword = "PaymentPassword";
        /**
         * 渠道自动扣款
         */
        String AutomaticallyDeduction = "AutomaticallyDeduction";
    }

    /**
     * 供货商设置
     */
    interface SupplierSettings {
        /**
         * 审核后自动上架
         */
        String AutomaticallyOnShelf = "AutomaticallyOnShelf";
    }

    interface BeanIgnore {
        /**
         * 常见BeanUtil ignore字段
         */
        String[] FAMILIAR_PROPERTIES = new String[] {"id", "createBy", "createTime", "updateBy", "updateTime"};
    }

}
