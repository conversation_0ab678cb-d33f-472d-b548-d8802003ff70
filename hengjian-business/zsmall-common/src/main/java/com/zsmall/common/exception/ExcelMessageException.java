package com.zsmall.common.exception;

import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.ExcelMessageEnum;

/**
 * Excel状态码异常类
 *
 * <AUTHOR>
 * @create 2022/6/6 15:27
 */
public class ExcelMessageException extends Exception {

    private LocaleMessage localeMessage;

    public ExcelMessageException() {
    }

    /**
     * 初始化Excel处理错误信息
     * @param excelMessageEnum
     */
    public ExcelMessageException(ExcelMessageEnum excelMessageEnum) {
        super(excelMessageEnum.buildLocalMessage().getZh_CN().toString());
        this.localeMessage = excelMessageEnum.buildLocalMessage();
    }

    /**
     * 初始化Excel处理错误信息
     * @param excelMessageEnum
     */
    public ExcelMessageException(LocaleMessage localeMessage) {
        super(localeMessage.getZh_CN().toString());
        this.localeMessage = localeMessage;
    }

    public ExcelMessageException(String message) {
        super(message);
    }

    public ExcelMessageException(String message, Throwable cause) {
        super(message, cause);
    }

    public ExcelMessageException(Throwable cause) {
        super(cause);
    }

    public ExcelMessageException(String message, Throwable cause, boolean enableSuppression,
                                 boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public LocaleMessage getLocaleMessage() {
        return localeMessage;
    }
}
