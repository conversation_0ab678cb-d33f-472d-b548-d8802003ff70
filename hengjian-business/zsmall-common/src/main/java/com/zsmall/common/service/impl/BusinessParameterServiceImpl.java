package com.zsmall.common.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.extend.utils.SystemEventUtils;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.service.BusinessParameterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessParameterServiceImpl implements BusinessParameterService {

    /**
     * 获取指定配置项。
     * @param parameterType 配置项类型
     * @return
     */
    protected String getBusinessParameter(BusinessParameterType parameterType) {
        log.info("获取指定配置项 = {}", parameterType);
        TimeInterval timer = DateUtil.timer();
        String configValue = SystemEventUtils.getSysConfigValue(parameterType.name());
        log.info("获取指定配置项 = {} 配置项值 = {} 耗时 = {}ms", parameterType, configValue, timer.intervalMs());
        return configValue;
    }


    /**
     * 获得自定义值类型内容的参数。
     *
     * @param parameterType
     * @param typeClz
     * @return
     */
    @Override
    public <T> T getValueFromType(BusinessParameterType parameterType, Class<T> typeClz) {
        log.info("获得自定义值类型内容的参数 parameterType = {}, typeClz = {}", parameterType, typeClz);
        String paramVal = getBusinessParameter(parameterType);

        T result = null;
        if (StrUtil.isNotBlank(paramVal)) {
            if (ObjectUtil.equals(typeClz, String.class)) {
                result = (T) paramVal;
            } else if (ObjectUtil.equals(typeClz, BigDecimal.class)) {
                result = (T) NumberUtil.toBigDecimal(paramVal);
            } else if (ObjectUtil.equals(typeClz, Double.class)) {
                result = (T) Double.valueOf(paramVal);
            } else if (ObjectUtil.equals(typeClz, JSONObject.class)) {
                result = (T) JSONUtil.parseObj(paramVal);
            } else if (ObjectUtil.equals(typeClz, JSONArray.class)) {
                result = (T) JSONUtil.parseArray(paramVal);
            } else if (ObjectUtil.equals(typeClz, Integer.class)) {
                result = (T) Integer.valueOf(paramVal);
            }
        }
        return result;
    }

    @Override
    public Double getValueFromDouble(BusinessParameterType parameterType) {
        return getValueFromType(parameterType, Double.class);
    }

    @Override
    public BigDecimal getValueFromBigDecimal(BusinessParameterType parameterType) {
        return getValueFromType(parameterType, BigDecimal.class);
    }

    @Override
    public Integer getValueFromInteger(BusinessParameterType parameterType) {
        return getValueFromType(parameterType, Integer.class);
    }

    @Override
    public String getValueFromString(BusinessParameterType parameterType) {
        return getValueFromType(parameterType, String.class);
    }

    /**
     * 获取JSONArray内容的参数
     *
     * @param parameterType {@link BusinessParameterType} 业务参数类型
     * @return {@link JSONArray}
     */
    @Override
    public JSONArray getValueFromJSONArray(BusinessParameterType parameterType) {
        return getValueFromType(parameterType, JSONArray.class);
    }

    /**
     * 获取JSONObject内容的参数
     * @param parameterType {@link BusinessParameterType} 业务参数类型
     * @return {@link JSONObject}
     */
    @Override
    public JSONObject getValueFromJSONObject(BusinessParameterType parameterType) {
        return getValueFromType(parameterType, JSONObject.class);
    }
}
