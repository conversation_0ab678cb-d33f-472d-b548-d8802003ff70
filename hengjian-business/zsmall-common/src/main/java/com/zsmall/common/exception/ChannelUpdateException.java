package com.zsmall.common.exception;

import com.zsmall.common.domain.dto.ChannelUpdateMessageDTO;
import lombok.Getter;

/**
 * 第三方渠道更新相关异常
 * <AUTHOR>
 */
@Getter
public class ChannelUpdateException extends Exception {

  private ChannelUpdateMessageDTO messageDTO;

  public ChannelUpdateException() {
    super();
  }

  public ChannelUpdateException(ChannelUpdateMessageDTO messageDTO) {
    super(messageDTO.getMessage());
    this.messageDTO = messageDTO;
  }

  public ChannelUpdateException(String message) {
    super(message);
  }

  public ChannelUpdateException(String message, Throwable cause) {
    super(message, cause);
  }

  public ChannelUpdateException(Throwable cause) {
    super(cause);
  }

  protected ChannelUpdateException(String message, Throwable cause, boolean enableSuppression,
    boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }
}
