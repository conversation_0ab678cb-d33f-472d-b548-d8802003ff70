package com.zsmall.common.exception;

import com.hengjian.common.core.domain.RStatusCodeBase;
import com.zsmall.common.domain.LocaleMessage;

/**
 * <AUTHOR>
 * @date 2024年6月18日  14:37
 * @description: 产品异常相关
 */
public class ProductException extends Exception{

    private LocaleMessage localeMessage;

    public ProductException() {
        super();
    }

    public ProductException(RStatusCodeBase RStatusCodeBase) {
        super(RStatusCodeBase.getMessage());
        this.localeMessage = LocaleMessage.byStatusCode(RStatusCodeBase);
    }

    public ProductException(String message) {
        super(message);
    }

    public ProductException(String message, Throwable cause) {
        super(message, cause);
    }

    public ProductException(Throwable cause) {
        super(cause);
    }

    protected ProductException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    /**
     * 获取国际化消息实体
     * @return
     */
    public LocaleMessage getLocaleMessage() {
        return localeMessage;
    }
}
