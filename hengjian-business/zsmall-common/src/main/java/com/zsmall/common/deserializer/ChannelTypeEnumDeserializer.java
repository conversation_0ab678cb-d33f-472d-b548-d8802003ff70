package com.zsmall.common.deserializer;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.zsmall.common.enums.common.ChannelTypeEnum;

import java.lang.reflect.Type;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/6/4 15:34
 */
public class ChannelTypeEnumDeserializer implements ObjectDeserializer {
    @Override
    public ChannelTypeEnum deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        Object value = parser.parse(); // 自动识别数字/字符串

        // 处理字符串类型枚举值 (如 "Shein-full")
        if (value instanceof String) {
            String strVal = ((String) value)
                .replace("-", "_");     // 统一大写

            // 优先按枚举名称匹配
            try {
                return ChannelTypeEnum.valueOf(strVal);
            } catch (IllegalArgumentException ignored) {}

            // 按自定义名称匹配 (如SHEIN_FULL)
            for (ChannelTypeEnum e : ChannelTypeEnum.values()) {
                if (e.name().equalsIgnoreCase(strVal)) return e;
            }
        }

        throw new IllegalArgumentException("无效枚举值: " + value);
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}
