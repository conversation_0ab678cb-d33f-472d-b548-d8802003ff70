package com.zsmall.common.domain.airwallex.payment.intents;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年3月5日  16:47
 * @description:
 */
@Data
public class PaymentMethod {

    private AchDirectDebit achDirectDebit;
    private AirwallexPay airwallexPay;
    private Alfamart alfamart;
    private Alipaycn alipaycn;
    private Alipayhk alipayhk;
    private Applepay applepay;
    private Atome atome;
    private AxsKiosk axsKiosk;
    private BacsDirectDebit bacsDirectDebit;
    private Bancontact bancontact;
    private BankTransfer bankTransfer;
    private BecsDirectDebit becsDirectDebit;
    private Bitpay bitpay;
    private Blik blik;
    private Boost boost;
    private PaymentMethodCard card;
    private String createdAt;
    private String customerid;
    private Dana dana;
    private DokuEwallet dokuEwallet;
    private Dragonpay dragonpay;
    private DuitNow duitNow;
    private EftDirectDebit eftDirectDebit;
    private Enets enets;
    private Eps eps;
    private Esun esun;
    private FamilyMart familyMart;
    private FPS fps;
    private Fpx fpx;
    private Gcash gcash;
    private Giropay giropay;
    private GoPay goPay;
    private Googlepay googlepay;
    private Grabpay grabpay;
    private HiLife hiLife;
    private String id;
    private Ideal ideal;
    private Indomaret indomaret;
    private JeniusPay jeniusPay;
    private Kakaopay kakaopay;
    private Klarna klarna;
    private Konbini konbini;
    private Linkaja linkaja;
    private Maxima maxima;
    private PaymentMethodMetadata metadata;
    private Multibanco multibanco;
    private Mybank mybank;
    private Narvesen narvesen;
    private OnlineBanking onlineBanking;
    private Ovo ovo;
    private P24 p24;
    private PayNow payNow;
    private Paybybankapp paybybankapp;
    private Payeasy payeasy;
    private Paypal paypal;
    private Paypost paypost;
    private Paysafecard paysafecard;
    private Paysafecash paysafecash;
    private Paysera paysera;
    private Payu payu;
    private PerlasTerminals perlasTerminals;
    private PromptPay promptPay;
    private RabbitLinePay rabbitLinePay;
    private SamKiosk samKiosk;
    private Satispay satispay;
    private SepaDirectDebit sepaDirectDebit;
    private SevenEleven sevenEleven;
    private ShopeePay shopeePay;
    private Skrill skrill;
    private Sofort sofort;
    private String status;
    private Tng tng;
    private Truemoney truemoney;
    private Trustly trustly;
    private String type;
    private String updatedAt;
    private Verkkopankki verkkopankki;
    private Wechatpay wechatpay;
    private Zip zip;
}
