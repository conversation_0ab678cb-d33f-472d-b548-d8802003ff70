package com.zsmall.common.handler;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * lty notes 此抽象模版负责抽离规定分销内部的业务流程,具体实现通过 第四层抽象模版去设计和规范
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 14:53
 */
@Slf4j
public abstract class AbstractOrderOperationHandler<J, D, V, O, P> {


    /**
     * 功能描述：初始消息保存
     *
     * @param d d
     * <AUTHOR>
     * @date 2024/06/25
     */
    public abstract void initialMessageSave(D d);

    /**
     * 功能描述：解析第三方数据
     *
     * @param j
     * @return {@link V }
     * <AUTHOR>
     * @date 2024/01/09
     */
    public abstract D parseThirdData(J j) throws Exception;

    /**
     * 功能描述：消息验证
     *
     * @param d
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/01/09
     */
    public abstract Boolean msgVerify(D d);

    /**
     * 功能描述：正式订单和项目输入
     *
     * @param map 地图
     * @param o   o
     * <AUTHOR>
     * @date 2024/01/09
     */
    public abstract O formalOrderAndItemEntry(Map<String, List> map, O o);

    /**
     * 功能描述：订单录入相关
     *
     * @param map 地图
     * <AUTHOR>
     * @date 2024/01/09
     */
    public abstract void formalOrderAboutEntry(Map<String, Object> map);


    /**
     * 功能描述：物流和金额相关
     *
     * @param d d
     * @return {@link V }
     * <AUTHOR>
     * @date 2024/01/09
     */
    public abstract Map<String, Object> msgForLogistics(D d, O o, Map<String, List> map);

    /**
     * 功能描述：商品信息
     *
     * @param d d
     * @return {@link V }
     * <AUTHOR>
     * @date 2024/01/09
     */
    public abstract Map<String, List> msgForItems(D d, O o);

    public abstract void getShippingLabels(List<Map<String, List>> orderData);

//    public abstract V msgForOrders(D d,O o);

    /**
     * 功能描述：三方录入模版  单数据录入
     *
     *
     * @param
     * <AUTHOR>
     * @date 2024/01/09
     */

    @Transactional(rollbackFor = Exception.class)
    public Boolean formalTripartiteEntry(J j, O o) throws Exception {
        // 临时计划 后期优化使用线程池,和批量保存批量更新
        /** 1. orders和订单附属对象异步处理数据填充,全部通过channelOrderNo关联
         *  2. 统一进行orders的保存,保存完根据channelOrderNo关联保存orderItem及其相关的类
         *
         * **/
        D d = parseThirdData(j);
        List<D> list = ordersDisassemble(d);
        for (D i : list) {
            if (msgVerify(i)) {
                o = thirdToDistribution(i, o);
                Map<String, List> map = msgForItems(i, o);
                formalOrderAndItemEntry(map, o);
                Map<String, Object> abooutMap = msgForLogistics(i, o, map);
                formalOrderAboutEntry(abooutMap);
            }
        }
        // todo 先把价格在pay之前改完,
        if (isNeedPay()) {
            ((AbstractOrderOperationHandler) AopContext.currentProxy()).payOrder(d);
        }

        return Boolean.TRUE;
    }


    @Transactional(rollbackFor = Exception.class)
    public ArrayList<String> formalTripartiteEntryForOpen(J j, O o, List<D> list) throws Exception {
        // 临时计划 后期优化使用线程池,和批量保存批量更新
        /** 1. orders和订单附属对象异步处理数据填充,全部通过channelOrderNo关联
         *  2. 统一进行orders的保存,保存完根据channelOrderNo关联保存orderItem及其相关的类
         *
         * **/
        ArrayList<String> os = new ArrayList<>();
        for (D i : list) {
            if (msgVerify(i)) {
                o = thirdToDistribution(i, o);
                Map<String, List> map = msgForItems(i, o);
                formalOrderAndItemEntry(map, o);
                Map<String, Object> abooutMap = msgForLogistics(i, o, map);
                formalOrderAboutEntry(abooutMap);
                String str= attachmentsFlow(i,o);
                os.add(str);
            }
        }
        // 原报文信息保存

//        if(isNeedPay()){
//            ((AbstractOrderOperationHandler) AopContext.currentProxy()).payOrderForOpen(d);
//        }
        return os;
    }

    /**
     * 功能描述：开放v2正式三方进入
     *
     * @param j j
     * @param o o
     * @return {@link ArrayList }<{@link String }>
     * <AUTHOR>
     * @date 2024/07/01
     */
    public ArrayList<String> formalTripartiteEntryForOpenTemplate(J j, O o) throws Exception {
        // 临时计划 后期优化使用线程池,和批量保存批量更新
        /** 1. orders和订单附属对象异步处理数据填充,全部通过channelOrderNo关联
         *  2. 统一进行orders的保存,保存完根据channelOrderNo关联保存orderItem及其相关的类
         *
         * **/
        D d = parseThirdData(j);
        List<D> list = ordersDisassemble(d);
        ArrayList<String> os = ((AbstractOrderOperationHandler) AopContext.currentProxy()).formalTripartiteEntryForOpen(j, o, list);
        // 此处异步 放到队列里去支付

        try {
            payOrderForAsync(d, true);
        } catch (Exception e) {
            log.error("支付失败", e);
        }
        return os;
    }

    /**
     * temu订单转化为分销订单
     *
     * @param j
     * @param o
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ArrayList<String> formalTripartiteEntryForTemu(J j, O o) throws Exception {

        /**
         * 临时计划 后期优化使用线程池,和批量保存批量更新
         * 1. orders和订单附属对象异步处理数据填充,全部通过channelOrderNo关联
         * 2. 统一进行orders的保存,保存完根据channelOrderNo关联保存orderItem及其相关的类
         */
         // 从xml中读取订单信息
        D d = parseThirdData(j);
        if(ObjectUtil.isNull(d)){
            return null;
        }
        // 根据订单信息中的item拆分订单
        List<D> list = ordersDisassemble(d);
        ArrayList<String> os = new ArrayList<>();

        for (D i : list) {
            // 验证订单信息
            if (msgVerify(i)) {
                //  把读取到的订单信息转化为分销订单信息
                o = thirdToDistribution(i, o);
                // 把读取到的订单信息转化为分销订单item信息
                Map<String, List> map = msgForItems(i, o);
                // 保存订单信息、订单item信息、itemProductSku信息
                formalOrderAndItemEntry(map, o);
                // 将订单信息 转化为 物流信息、主订单地址信息、订单item价格信息，orderLogisticsInfos、addressInfos、itemPrices
                Map<String, Object> abooutMap = msgForLogistics(i, o, map);
                //  保存上一步转化为物流信息、主订单地址信息、订单item价格信息
                formalOrderAboutEntry(abooutMap);
                // 附件处理
//                String str= attachmentsFlow(i,o);
//                os.add(str);
            }
        }
        if(isNeedPay(d)){
            ((AbstractOrderOperationHandler) AopContext.currentProxy()).payOrderForAsync(d, null);
        }

        return os;
    }


    /**
     * AmazonSC订单转化为分销订单
     *
     * @param j
     * @param o
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ArrayList<String> formalTripartiteEntryForSC(J j, O o) throws Exception {

        /**
         * 临时计划 后期优化使用线程池,和批量保存批量更新
         * 1. orders和订单附属对象异步处理数据填充,全部通过channelOrderNo关联
         * 2. 统一进行orders的保存,保存完根据channelOrderNo关联保存orderItem及其相关的类
         */
        // 从xml中读取订单信息
        D d = parseThirdData(j);
        if(ObjectUtil.isNull(d)){
            return null;
        }
        // 根据订单信息中的item拆分订单
        List<D> list = ordersDisassemble(d);
        ArrayList<String> os = new ArrayList<>();
//        List<Map<String, List>> splitiOrders = new ArrayList<>();

        for (D i : list) {
            // 验证订单信息
            if (msgVerify(i)) {
                //  把读取到的订单信息转化为分销订单信息
                o = thirdToDistribution(i, o);
                // 把读取到的订单信息转化为分销订单item信息
                Map<String, List> map = msgForItems(i, o);
                // 保存订单信息、订单item信息、itemProductSku信息
                formalOrderAndItemEntry(map, o);
                // 将订单信息 转化为 物流信息、主订单地址信息、订单item价格信息，orderLogisticsInfos、addressInfos、itemPrices
                Map<String, Object> abooutMap = msgForLogistics(i, o, map);
                //  保存上一步转化为物流信息、主订单地址信息、订单item价格信息
                formalOrderAboutEntry(abooutMap);
//                // 面单处理
//                List<O> orders = new ArrayList<>();
//                orders.add(o);
//                map.put("orders",orders);
//                splitiOrders.add(map);
            }
        }
        //获取面单 自提获取面单
//        if (!CollectionUtil.isEmpty(splitiOrders)) {
//            getShippingLabels(splitiOrders);
//        }
        if(isNeedPay(d)){
            ((AbstractOrderOperationHandler) AopContext.currentProxy()).payOrderForAsync(d, null);
        }

        return os;
    }

        /**
     * VCDF订单转化为分销订单
     *
     * @param j
     * @param o
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ArrayList<String> formalTripartiteEntryForAmazonVc(J j, O o) throws Exception {

        /**
         * 临时计划 后期优化使用线程池,和批量保存批量更新
         * 1. orders和订单附属对象异步处理数据填充,全部通过channelOrderNo关联
         * 2. 统一进行orders的保存,保存完根据channelOrderNo关联保存orderItem及其相关的类
         */
         // 从xml中读取订单信息
        D d = parseThirdData(j);
        if(ObjectUtil.isNull(d)){
            return null;
        }
        // 根据订单信息中的item拆分订单
        List<D> list = ordersDisassemble(d);
        ArrayList<String> os = new ArrayList<>();
        List<Map<String, List>> splitiOrders = new ArrayList<>();

        for (D i : list) {
            // 验证订单信息
            if (msgVerify(i)) {
                //  把读取到的订单信息转化为分销订单信息
                o = thirdToDistribution(i, o);
                // 把读取到的订单信息转化为分销订单item信息
                Map<String, List> map = msgForItems(i, o);

                // 保存订单信息、订单item信息、itemProductSku信息
                formalOrderAndItemEntry(map, o);

                // 将订单信息 转化为 物流信息、主订单地址信息、订单item价格信息，orderLogisticsInfos、addressInfos、itemPrices
                Map<String, Object> abooutMap = msgForLogistics(i, o, map);
                //  保存上一步转化为物流信息、主订单地址信息、订单item价格信息
                formalOrderAboutEntry(abooutMap);
                List<O> orders = new ArrayList<>();
                orders.add(o);
                map.put("orders",orders);
                splitiOrders.add(map);
                // 附件处理
//                String str= attachmentsFlow(i,o);
//                os.add(str);
            }
        }
        //获取面单 自提获取面单
        if (!CollectionUtil.isEmpty(splitiOrders)) {
            getShippingLabels(splitiOrders);
        }

        if(isNeedPay(d)){
            ((AbstractOrderOperationHandler) AopContext.currentProxy()).payOrderForAsync(d, null);
        }

        return os;
    }

    /**
     * 功能描述：是需要付钱
     *
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/01/21
     */
    public abstract Boolean isNeedPay();

    /**
     * 功能描述：附件关于
     *
     * <AUTHOR>
     * @date 2024/05/14
     */
    public abstract String attachmentsFlow(D d, O o);

    /**
     * 功能描述：是需要支付
     *
     * @param d d
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/02/22
     */
    public abstract Boolean isNeedPay(D d);

    public abstract Boolean payOrder(D d) throws Exception;

    public abstract Boolean payOrderForAsync(D d, Boolean isAsync) throws Exception;

    /**
     * 功能描述：第三方数据拆解转换为分销订单信息
     *
     * @param d
     * @return {@link O }
     * <AUTHOR>
     * @date 2024/01/09
     */
    public abstract O thirdToDistribution(D d, O o) throws ParseException;


    /**
     * 功能描述：订单拆解
     *
     * @param d d
     * @return {@link List }<{@link D }>
     * <AUTHOR>
     * @date 2024/01/10
     */
    public abstract List<D> ordersDisassemble(D d);


    /**
     * 功能描述：三方物流更新
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/01/10
     */
    public abstract R tripartiteUpdate(P p);

    /**
     * 功能描述：三方确认收货(供应商发货,分销商收货)
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/01/10
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean tripartiteReceipt(P p) {
        Boolean isDeliverGoods = tripartiteDeliverGoods(p);
        Boolean isReceiptGoods = tripartiteReceiptGoods(p);
        //
        return isReceiptGoods && isDeliverGoods;
    }

    /**
     * 功能描述：三方发货
     *
     * @param p p
     * <AUTHOR>
     * @date 2024/01/18
     */
    public abstract Boolean tripartiteDeliverGoods(P p);

    /**
     * 功能描述：三方收货
     *
     * @param p p
     * <AUTHOR>
     * @date 2024/01/18
     */
    public abstract Boolean tripartiteReceiptGoods(P p);

    @Transactional(rollbackFor = Exception.class)
    public abstract R<Void> test();


    /**
     * 功能描述：三方数据录入模板v2- 多线程优化 流程优化
     *
     * @param
     * @param j               j
     * @param channelTypeEnum
     * @return {@link ArrayList }<{@link String }>
     * <AUTHOR>
     * @date 2024/06/28
     */
    @Transactional(rollbackFor = Exception.class)
    public void formalTripartiteEntryTemplate(J j, BusinessTypeMappingEnum mappingEnum,
                                              ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                              ChannelTypeEnum channelTypeEnum) {
        // 检查数据 前三层实际为防腐层
        List<D> ds = parseThirdDataForList(j);
        // 存放业务数据 key 为 order / orderItem / itemProductSku value为具体的数据对象
        List<D> list = ordersDisassembleForList(ds, mappingEnum);
        if(!CollUtil.isNotEmpty(list)){
           return;
        }
        // 开多线程 异步   保存/ map信息更新 /保存
        ConcurrentHashMap<String, List<Object>> businessMap = new ConcurrentHashMap<>();
        ThreadPoolExecutor executor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
        CountDownLatch downLatch = new CountDownLatch(list.size());
        log.info("线程id:{}--|订单录入开始:录入记录总数{}",Thread.currentThread().getId(), list.size());
        for (D i : list) {

            executor.submit(() -> {
                // 验证订单信息
                try {
                    if (msgVerify(i)) {
                        // 把读取到的订单信息转化为分销订单信息 这一批录入的方法应该是一致的,部分不一致的例如:计价规则/渠道/订单状态/订单发货方式等
                        // 为了确保模版方法的简洁,此处的业务应该单独抽出一套新的模版去实现,以确保后续的业务变更或扩展不会影响到抽象模版,这样的设计是为了保证抽象模版的稳定性,不会有影响到其他类似 temu接入的业务
                        orderOperationHandler(i, businessMap, businessNos, channelTypeEnum, mappingEnum);

                    }
                } catch (Exception e) {
                    log.error("订单录入失败", e);
                } finally {
                    downLatch.countDown();
                }

            });
        }
        try {
            downLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        // 保存前的批量价格计算,businessMap内的订单数量和ds是不一致的,所以后续的业务流程需要注意,如果是已经有mapping的业务,实际要把这样的数据分别处理,
        priceOperation(businessMap, businessNos, channelTypeEnum,mappingEnum);
        orderOperationHandlerSave(channelTypeEnum, businessMap);

    }

    public abstract void orderOperationHandler(D i, ConcurrentHashMap<String, List<Object>> businessMap,
                                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum);

    public abstract void priceOperation( ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                        ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum);

    /**
     * 功能描述：业务批处理保存
     *
     * @param businessMap 商业地图
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                                   ConcurrentHashMap<String, List<Object>> businessMap);


    /**
     * 功能描述：订单细化/移除不符合业务的订单或子订单
     *
     * @param ds          ds
     * @param mappingEnum 映射枚举
     * @return {@link List }<{@link D }>
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract List<D> ordersDisassembleForList(List<D> ds, BusinessTypeMappingEnum mappingEnum);

    public abstract List<D> parseThirdDataForList(J j);
}
