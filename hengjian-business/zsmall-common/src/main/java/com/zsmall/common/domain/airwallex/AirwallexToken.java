package com.zsmall.common.domain.airwallex;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/27 11:07
 */
@Data
@AllArgsConstructor
public class AirwallexToken {
    @JsonProperty("expires_at")
    @JSONField(name="expires_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expiresAt;
    private String token;
}
