package com.zsmall.common.exception;

import com.hengjian.common.core.domain.RStatusCodeBase;
import com.zsmall.common.domain.LocaleMessage;

/**
 * 钱包相关异常
 * <AUTHOR>
 * @date 2023/6/14
 */
public class WalletException extends Exception {

    private LocaleMessage localeMessage;
    private RStatusCodeBase statusCode;

    public WalletException() {
        super();
    }

    public WalletException(RStatusCodeBase statusCode) {
        super(statusCode.getMessage());
        this.localeMessage = LocaleMessage.byStatusCode(statusCode);
        this.statusCode = statusCode;
    }

    public LocaleMessage getLocaleMessage() {
        return localeMessage;
    }

    public RStatusCodeBase getStatusCode() {
        return statusCode;
    }
}
