package com.zsmall.common.domain;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import org.checkerframework.checker.units.qual.A;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年7月9日  10:35
 * @description: 支付金额币种
 */
@Data
@Accessors(chain = true)
@Builder
public class CurrencyAmountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String currency;

    private String currencySymbol;

    private BigDecimal amount;

    private String storageFeeId;

}
