package com.zsmall.common.handler;

import com.alibaba.fastjson.JSONObject;

/**
 * lty notes
 *
 * <AUTHOR> <PERSON>
 * @create 2024/1/30 18:15
 */
public abstract class AbstractOrderBaseHandler<T,E> {

    /**
     * 功能描述：保存订单 关于
     *
     * @param json json格式
     * @param t    t
     * <AUTHOR>
     * @date 2024/01/30
     */
    public abstract void saveOrderBefore(JSONObject json, Class<T> t);

    public abstract void saveOrder(JSONObject json, Class<T> t);
}
