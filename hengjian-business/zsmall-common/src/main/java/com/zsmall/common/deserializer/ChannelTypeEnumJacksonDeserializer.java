package com.zsmall.common.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.zsmall.common.enums.common.ChannelTypeEnum;

import java.io.IOException;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/6/4 15:53
 */
public class ChannelTypeEnumJacksonDeserializer extends StdDeserializer<ChannelTypeEnum> {

    protected ChannelTypeEnumJacksonDeserializer() {
        super(ChannelTypeEnum.class);
    }

    @Override
    public ChannelTypeEnum deserialize(JsonParser jsonParser, DeserializationContext ctx) throws IOException {
        // 如果当前token是字符串，则按字符串解析
        if (jsonParser.currentToken() == JsonToken.VALUE_STRING) {
            String text = jsonParser.getText().trim();
            // 将横线转为下划线
            String normalized = text.replace('-', '_');
            // 尝试根据名称查找枚举（忽略大小写）
            for (ChannelTypeEnum enumValue : ChannelTypeEnum.values()) {
                // 忽略大小写匹配
                if (enumValue.name().equalsIgnoreCase(normalized)) {
                    return enumValue;
                }
            }
            // 如果没有匹配到，可以尝试按数值解析（如果传入的是数字字符串）
            try {
                int code = Integer.parseInt(text);
                for (ChannelTypeEnum enumValue : ChannelTypeEnum.values()) {
                    if (enumValue.getChannelId() == code) {
                        return enumValue;
                    }
                }
            } catch (NumberFormatException ignored) {
            }
        } else if (jsonParser.currentToken() == JsonToken.VALUE_NUMBER_INT) {
            // 如果当前token是整数，则按数值解析
            int code = jsonParser.getIntValue();
            for (ChannelTypeEnum enumValue : ChannelTypeEnum.values()) {
                if (enumValue.getChannelId() == code) {
                    return enumValue;
                }
            }
        }
        // 如果都不匹配，可以返回null或者抛出异常，根据业务需要
        return null;
    }
}
