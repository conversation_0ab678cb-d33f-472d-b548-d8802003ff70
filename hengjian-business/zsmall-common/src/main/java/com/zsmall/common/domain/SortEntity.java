package com.zsmall.common.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.SortEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类基类
 *
 * <AUTHOR>
 * @create 2022/1/7 17:10
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class SortEntity extends NoDeptBaseEntity {

    /**
     * 排序字段
     */
    @TableField(exist = false)
    private String sortValue;

    /**
     * 排序类型
     */
    @TableField(exist = false)
    private SortEnum sortType;


}
