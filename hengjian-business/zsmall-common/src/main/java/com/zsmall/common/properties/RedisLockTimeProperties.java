package com.zsmall.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022-10-10
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "locktime")
public class RedisLockTimeProperties {

  //锁货库存锁持续时间（秒）
  private Long productActivityStockLockTime;
}
