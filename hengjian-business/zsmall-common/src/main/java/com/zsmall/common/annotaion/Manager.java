package com.zsmall.common.annotaion;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * lty notes 接口复合管理层,用于事务管理
 *
 * <AUTHOR> Theo
 * @create 2023/12/18 17:14
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface Manager {

    @AliasFor(annotation = Component.class)
    String value() default "";

}
