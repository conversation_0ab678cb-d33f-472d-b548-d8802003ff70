package com.zsmall.common.constant;

/**
 * 订单退款规则相关常量
 * <AUTHOR>
 * @date 2023/2/6
 */
public interface OrderRefundRuleConstant {

  /**
   * 是否需要员工审核
   */
  interface WhetherReviewMd {

    /**
     * 不需要
     */
    Integer NOT_REQUIRED = 10;

    /**
     * 需要
     */
    Integer REQUIRED = 20;

    /**
     * 超阈值需要
     */
    Integer THRESHOLD_EXCEEDED = 30;

  }

  /**
   * 是否支持修改退款金额
   */
  interface WhetherSupportModifyAmount {

    /**
     * 不支持
     */
    Integer UNSUPPORTED = 10;

    /**
     * 支持
     */
    Integer SUPPORT = 20;

  }

  /**
   * 是否需要提供图片举证
   */
  interface WhetherProvidePictures {

    /**
     * 不需要
     */
    Integer NOT_REQUIRED = 10;

    /**
     * 需要
     */
    Integer REQUIRED = 20;

  }


  /**
   * 是否还原库存
   */
  interface WhetherRestoreInventory {

    /**
     * 不还原
     */
    Integer NO_RESTORE = 10;

    /**
     * 整单还原
     */
    Integer FULL_ORDER_RESTORE = 20;

    /**
     * 可执行金额为0则还原
     */
    Integer EXECUTABLE_AMOUNT_ZERO_RESTORE = 30;

  }

}
