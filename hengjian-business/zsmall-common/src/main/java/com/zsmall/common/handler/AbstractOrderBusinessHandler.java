package com.zsmall.common.handler;

import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * lty notes 此抽象模版负责具体业务的实现
 *
 * <AUTHOR> Theo
 * @create 2024/7/2 23:18
 */
@Slf4j
public abstract class AbstractOrderBusinessHandler<D> {

    /**
     * 功能描述：订单操作hanlder
     *
     * @param i               我
     * @param businessMap     商业地图
     * @param businessNos     业务编号
     * @param channelTypeEnum
     * @param mappingEnum
     * <AUTHOR>
     * @date 2024/07/03
     */
    public void orderOperationHandler(D i, ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum){
        // 订单填充异步
        thirdToOrderItemPrice(i, businessMap, businessNos, channelTypeEnum, mappingEnum);
        thirdToAddressAndLogisticsInfoAndTracking(i, businessMap, businessNos, channelTypeEnum, mappingEnum);
        thirdToOrderItemProductSku(i, businessMap, businessNos, channelTypeEnum,mappingEnum);
        thirdToOrderItem(i, businessMap, businessNos, channelTypeEnum,mappingEnum);
        thirdToOrder(i, businessMap, businessNos, channelTypeEnum,mappingEnum);
//        priceOperation(i, businessMap, businessNos, channelTypeEnum,mappingEnum);
    }

    /**
     * 功能描述：价格操作
     *
     * @param i               我
     * @param businessMap     商业地图
     * @param businessNos     企业编号
     * @param channelTypeEnum 通道类型枚举
     * @param mappingEnum     映射枚举
     * <AUTHOR>
     * @date 2024/08/01
     */
    public abstract void priceOperation(D i, ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum);

//    public void orderOperationHandler(D i, ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
//                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum){
//        // 订单填充异步
//        CompletableFuture<Void> priceAsync = CompletableFuture.runAsync(() -> thirdToOrderItemPrice(i, businessMap, businessNos, channelTypeEnum, mappingEnum));
//        CompletableFuture<Void> addressAsync = CompletableFuture.runAsync(() -> thirdToAddressAndLogisticsInfoAndTracking(i, businessMap, businessNos, channelTypeEnum, mappingEnum));
//        CompletableFuture<Void> productSkuAsync = CompletableFuture.runAsync(() -> thirdToOrderItemProductSku(i, businessMap, businessNos, channelTypeEnum,mappingEnum));
//        CompletableFuture<Void> orderItemAsync = CompletableFuture.runAsync(() -> thirdToOrderItem(i, businessMap, businessNos, channelTypeEnum,mappingEnum));
//        CompletableFuture<Void> orderAsync = CompletableFuture.runAsync(() -> thirdToOrder(i, businessMap, businessNos, channelTypeEnum,mappingEnum));
//        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(priceAsync, addressAsync,productSkuAsync,orderItemAsync,orderAsync);
//        try {
//            combinedFuture.get();
//        } catch (InterruptedException e) {
//            // 处理线程中断异常
//            Thread.currentThread().interrupt(); // 保留中断状态
//            log.error("CompletableFuture execution interrupted", e);
//        } catch (ExecutionException e) {
//            // 处理执行异常，获取并打印原始异常
//            Throwable cause = e.getCause();
//            if (cause instanceof CompletionException) { // 如果CompletableFuture的runAsync内部抛出了异常，会被包装成CompletionException
//                cause = cause.getCause(); // 再取一次原因，因为CompletionException也封装了原始异常
//            }
//            log.error("CompletableFuture execution failed", cause);
//        }
////        thirdToOrderItemPrice(i, businessMap, businessNos,channelTypeEnum);
////        thirdToAddressAndLogisticsInfoAndTracking(i, businessMap, businessNos,channelTypeEnum);
////        thirdToOrderItemProductSku(i, businessMap, businessNos,channelTypeEnum);
////        thirdToOrderItem(i, businessMap, businessNos,channelTypeEnum);
////        thirdToOrder(i, businessMap, businessNos,channelTypeEnum);
//
//    }

    public abstract void thirdToAddressAndLogisticsInfoAndTracking(D i, ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos, ChannelTypeEnum channelTypeEnum,
                                                                   BusinessTypeMappingEnum mappingEnum);


    /**
     * 功能描述：第三方订购商品价格
     *
     * @param i           我
     * @param businessMap 商业地图
     * @param businessNos 业务编号
     * @param mappingEnum
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void thirdToOrderItemPrice(D i, ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos, ChannelTypeEnum channelTypeEnum,
                                               BusinessTypeMappingEnum mappingEnum);



    /**
     * 功能描述：第三方订购商品sku转换
     *
     * @param i           我
     * @param businessMap 商业地图
     * @param businessNos 业务编号
     * @param mappingEnum
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void thirdToOrderItemProductSku(D i, ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                                    ChannelTypeEnum channelTypeEnum,
                                                    BusinessTypeMappingEnum mappingEnum);

    /**
     * 功能描述：第三方 订单项目转换
     *
     * @param i           我
     * @param businessMap 商业地图
     * @param businessNos 业务编号
     * @param mappingEnum
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void thirdToOrderItem(D i, ConcurrentHashMap<String, List<Object>> businessMap, ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                          ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum);

    /**
     * 功能描述：三方转换为订单
     *
     * @param i           我
     * @param businessMap 商业地图
     * @param businessNos
     * @param mappingEnum
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void thirdToOrder(D i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum);

    /**
     * 功能描述：业务批处理保存
     *
     * @param businessMap 商业地图
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void businessBatchSave(ConcurrentHashMap<String, List<Object>> businessMap,
                                           ChannelTypeEnum channelTypeEnum);
}
