package com.zsmall.common.service;

import com.zsmall.common.domain.airwallex.payment.intents.CreatePaymentIntentRequest;
import com.zsmall.common.domain.airwallex.payment.intents.CreatePaymentIntentResponse;

/**
 * <AUTHOR>
 * @date 2024年3月5日  18:42
 * @description: Airwallex service
 */
public interface AirwallexService {

    /**
     * 创建支付订单 调用 Create a PaymentIntent 接口
     * @param createPaymentIntentRequest
     * @return
     */
    CreatePaymentIntentResponse createPaymentIntent(CreatePaymentIntentRequest createPaymentIntentRequest);
}
