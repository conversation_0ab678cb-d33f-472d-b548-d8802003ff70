package com.zsmall.common.exception;

import com.hengjian.common.core.domain.RStatusCodeBase;
import com.zsmall.common.domain.LocaleMessage;

/**
 * 订单支付相关异常
 * <AUTHOR>
 * @date 2023/6/14
 */
public class OrderPayException extends Exception {

    private LocaleMessage localeMessage;

    public OrderPayException() {
        super();
    }

    public OrderPayException(RStatusCodeBase RStatusCodeBase) {
        super(RStatusCodeBase.getMessage());
        this.localeMessage = LocaleMessage.byStatusCode(RStatusCodeBase);
    }

    public OrderPayException(LocaleMessage localeMessage) {
        super(localeMessage.toMessage());
        this.localeMessage = localeMessage;
    }

    public OrderPayException(String message) {
        super(message);
    }

    public OrderPayException(String message, Throwable cause) {
        super(message, cause);
    }

    public OrderPayException(Throwable cause) {
        super(cause);
    }

    protected OrderPayException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public LocaleMessage getLocaleMessage() {
        return localeMessage;
    }
}
