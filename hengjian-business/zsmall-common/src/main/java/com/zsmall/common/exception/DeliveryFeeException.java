package com.zsmall.common.exception;

import com.hengjian.common.core.domain.RStatusCodeBase;
import com.zsmall.common.domain.LocaleMessage;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/8/5 16:50
 */
public class DeliveryFeeException extends Exception{
    private LocaleMessage localeMessage;
    private RStatusCodeBase statusCode;
    public DeliveryFeeException() {
        super();
    }

    public DeliveryFeeException(RStatusCodeBase RStatusCodeBase) {
        super(RStatusCodeBase.getMessage());
        this.localeMessage = LocaleMessage.byStatusCode(RStatusCodeBase);
    }

    public DeliveryFeeException(String message) {
        super(message);
    }

    public DeliveryFeeException(String message, Throwable cause) {
        super(message, cause);
    }

    public DeliveryFeeException(Throwable cause) {
        super(cause);
    }

    protected DeliveryFeeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    /**
     * 获取国际化消息实体
     * @return
     */
    public LocaleMessage getLocaleMessage() {
        return localeMessage;
    }
}
