package com.zsmall.common.support;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.service.BusinessParameterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 全局业务支持
 *
 * <AUTHOR>
 * @date 2023/6/29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GlobalBusinessSupport {

    private final BusinessParameterService businessParameterService;

    @InMethodLog("获取承运商转换Map")
    public Map<String, String> getCarrierSwitchMap() {
        Map<String, String> carrierMapRedis = new HashMap<>();
        try {
            Boolean hasKey = RedisUtils.hasKey(RedisConstants.ZSMALL_CARRIER_SWITCH);
            if (hasKey) {
                carrierMapRedis = RedisUtils.getCacheMap(RedisConstants.ZSMALL_CARRIER_SWITCH);
            } else {
                // 承运商转换
                JSONObject HENGJIAN_CARRIER_SWITCH = businessParameterService.getValueFromJSONObject(BusinessParameterType.HENGJIAN_CARRIER_SWITCH);
                Set<String> keySet = HENGJIAN_CARRIER_SWITCH.keySet();
                for (String key : keySet) {
                    JSONArray carrierArray = HENGJIAN_CARRIER_SWITCH.getJSONArray(key);
                    for (Object obj : carrierArray) {
                        String carrier = obj.toString();
                        carrierMapRedis.put(carrier, key);
                    }
                }
                RedisUtils.setCacheMap(RedisConstants.ZSMALL_CARRIER_SWITCH, carrierMapRedis);
                RedisUtils.expireCacheMap(RedisConstants.ZSMALL_CARRIER_SWITCH, Duration.ofHours(24));
            }
        } catch (Exception e) {
            log.error("获取承运商转换Map失败，原因 {}", e.getMessage(), e);
        }
        return carrierMapRedis;
    }
}
