package com.zsmall.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件相关Nacos 配置的内容会根据引用的common保的服务读取的配置yml文件内容而不同
 * <AUTHOR>
 * @create 2022/4/19 10:02
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file")
public class FileProperties {

  // 显示图片是否压缩
  private boolean showImageCompress = false;
  // 图片压缩前缀
  private String imageCompressSuffix;
  // 图片压缩后缀
  private String imageCompressPrefix;

  // 缓存文件保存路径
  private String tempSavePath;
  // 快递标签保存路径
  private String shippingLabelSavePath;
  // 模板文件保存路径
  private String templateSavePath;

}
