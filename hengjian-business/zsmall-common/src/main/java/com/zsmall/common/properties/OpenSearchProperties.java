package com.zsmall.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 亚马逊OpenSearch配置类
 * <AUTHOR>
 * @create 2022/4/11 17:02
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "amazon.opensearch")
public class OpenSearchProperties {

  private boolean generateOSTask = true;

  private String domain;

  private String username;

  private String password;

  private Map<String, String> index;

}
