package com.zsmall.common.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-30 11:14:14
 * @description 调用ERP算价接口请求参数
 */
@Data
public class DeliveryFeeByErpRequest {
    //分销商ID
    private String distributorTenantId;
    //供应商ID
    private String supplierTenantId;
    //请求ID
    private String requestId;
    //渠道店铺名称 供应商
    private String channelFlag;
    //发货仓信息
    private List<String> orgWarehouseCodeList;
    //收件邮编信息
    private String postcode;
    //商品信息
    private List<ProductItem> skuList;
    //发货物流公司
    private List<String> carrierCodes;
    private String logisticCode;
    //收货国家编码
    private String countryCode;
    /**
     * 商品信息
     */
    @Data
    public static class ProductItem {
        //商品sku
        @NotNull
        private String erpSku;
        //商品数量
        @NotNull
        private Integer quantity;

    }
}
