package com.zsmall.common.domain;

import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.RStatusCodeBase;
import com.hengjian.common.core.utils.MessageUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 国际化消息实体类
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@AllArgsConstructor
public class LocaleMessage {

    private StringBuilder zh_CN = new StringBuilder();
    private StringBuilder en_US = new StringBuilder();
    private StringBuilder ja_JP = new StringBuilder();

    private static final List<String> langList = new ArrayList<>() {{
        add("zh_CN");
        add("en_US");
        add("ja_JP");
    }};

    public LocaleMessage() {}

    public LocaleMessage(Map<String, StringBuilder> langBuilderMap) {
        langBuilderMap.forEach((key, value) -> ReflectUtil.setFieldValue(this, key, value));
    }

    /**
     * 通用消息构造，各语种通用一个消息
     * @param common
     */
    public LocaleMessage(String common) {
        this.zh_CN.append(common);
        this.en_US.append(common);
        this.ja_JP.append(common);
    }

    public LocaleMessage(String zh_CN, String en_US) {
        this.zh_CN.append(zh_CN);
        this.en_US.append(en_US);
        this.ja_JP.append(en_US);
    }

    public static LocaleMessage parseByJSONStr(String jsonStr) {
        JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
        LocaleMessage localeMessage = new LocaleMessage();

        for (String lang : langList) {
            String message = jsonObject.getStr(lang);
            if (StrUtil.isBlank(message)) {
                message = jsonObject.getStr("en_US");
            } else {
                message = jsonObject.getStr(lang);
            }

            StringBuilder builder = ReflectUtil.invoke(localeMessage, "get" + StrUtil.upperFirst(lang));
            builder.append(message);
        }
        return localeMessage;
    }

    public static LocaleMessage byStatusCode(RStatusCodeBase statusCode) {
        return new LocaleMessage().append(statusCode);
    }

    public static JSONObject byStatusCodeToJSON(RStatusCodeBase statusCode) {
        return new LocaleMessage().append(statusCode).toJSON();
    }

    public static JSONObject toJSON(String zh_CN, String en_US) {
        return new LocaleMessage(zh_CN, en_US).toJSON();
    }

    public static JSONObject toJSON(String message) {
        return new LocaleMessage(message, message).toJSON();
    }


    public JSONObject toJSON() {
        return JSONUtil.parseObj(this);
    }

    public String toJSONStr() {
        return JSONUtil.toJsonStr(this);
    }

    public String toJsonPrettyStr() {
        return JSONUtil.toJsonPrettyStr(this);
    }

    /**
     * 根据当前语种转换成对应的消息
     * @return
     */
    public String toMessage() {
        Locale locale = LocaleContextHolder.getLocale();
        if (Locale.SIMPLIFIED_CHINESE.equals(locale)) {
            return zh_CN.toString();
        } else if (Locale.JAPAN.equals(locale)) {
            return ja_JP.toString();
        } else {
            return en_US.toString();
        }
    }

    /**
     * 与另一个消息实体拼接
     * @param localeMessage
     * @return
     */
    public LocaleMessage append(LocaleMessage localeMessage) {
        this.zh_CN.append(localeMessage.getZh_CN());
        this.en_US.append(localeMessage.getEn_US());
        this.ja_JP.append(localeMessage.getJa_JP());
        return this;
    }
    /**
     * 与另一个消息实体拼接
     * @param localeMessage
     * @return
     */
    public LocaleMessage append(LocaleMessage localeMessage,String msg) {
        this.zh_CN.append(localeMessage.getZh_CN()).append(msg);
        this.en_US.append(localeMessage.getEn_US()).append(msg);
        this.ja_JP.append(localeMessage.getJa_JP()).append(msg);
        return this;
    }
    /**
     * 拼接错误码消息
     * @param statusCode
     * @return
     */
    public LocaleMessage append(RStatusCodeBase statusCode) {
        this.zh_CN.append(MessageUtils.message(Locale.SIMPLIFIED_CHINESE, statusCode.getMessageCode(), statusCode.getArgs()));
        this.en_US.append(MessageUtils.message(Locale.US, statusCode.getMessageCode(), statusCode.getArgs()));
        this.ja_JP.append(MessageUtils.message(Locale.JAPAN, statusCode.getMessageCode(), statusCode.getArgs()));
        return this;
    }

    public LocaleMessage appendSurround(LocaleMessage localeMessage, String prefix, String suffix) {
        this.zh_CN.append(StrJoiner.of("", prefix, suffix).append(localeMessage.zh_CN).toString());
        this.en_US.append(StrJoiner.of("", prefix, suffix).append(localeMessage.en_US).toString());
        this.ja_JP.append(StrJoiner.of("", prefix, suffix).append(localeMessage.ja_JP).toString());
        return this;
    }

    public boolean hasData() {
        return this.zh_CN.length() > 0 || this.en_US.length() > 0 || this.ja_JP.length() > 0;
    }



}
