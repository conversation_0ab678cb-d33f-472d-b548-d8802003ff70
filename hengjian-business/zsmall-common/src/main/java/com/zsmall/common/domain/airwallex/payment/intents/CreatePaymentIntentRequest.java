package com.zsmall.common.domain.airwallex.payment.intents;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年3月5日  16:04
 * @description: Create a PaymentIntent 接口参数
 */
@Data
@Accessors(chain = true)
public class CreatePaymentIntentRequest {

    private BigDecimal amount;
    private String connected_account_id;
    private String currency;
    private Customer customer;
    private String customer_id;
    private String descriptor;
    private String merchant_order_id;
    private Metadata metadata;
    private Order order;
    private PaymentMethodOptions payment_method_options;
    private String request_id;
    private String return_url;
    private RiskControlOptions risk_control_options;
}
