package com.zsmall.common.exception;

/**
 * lty notes
 *
 * <AUTHOR> <PERSON>
 * @create 2025/6/3 15:03
 */
public class ShippingValidationException extends RuntimeException{
    private final String errorCode; // 可选：定义错误码便于分类处理
    public ShippingValidationException(String message) {
        super(message);
        this.errorCode = "SHIPPING_SKU_MISMATCH";
    }
    public String getErrorCode() { return errorCode; }
}
