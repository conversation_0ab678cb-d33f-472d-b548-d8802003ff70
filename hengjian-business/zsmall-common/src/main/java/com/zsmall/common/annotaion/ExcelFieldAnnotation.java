package com.zsmall.common.annotaion;

import java.lang.annotation.*;

/**
 * Excel导入DTO字段注解
 *
 * <AUTHOR>
 * @date 2023/1/12
 */
// 是否生成注解文档
@Documented
// 标注这个注解的注解保留时期
@Retention(RetentionPolicy.RUNTIME)
// 标注这个类它可以标注的位置
@Target({ElementType.FIELD})
public @interface ExcelFieldAnnotation {

    /**
     * 是否必填
     *
     * @return
     */
    boolean required() default false;

    /**
     * 表格中的列名
     * @return
     */
    String column() default "";
}
