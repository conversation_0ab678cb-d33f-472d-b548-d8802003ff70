package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.system.domain.SysOss;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokDistrictInfo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.handler.AbstractOrderBusinessHandler;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.lottery.support.PriceBussinessV2Support;
import com.zsmall.order.biz.manager.OrderAndItemManager;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.PriceSupport;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.biz.support.WholesaleOrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.iservice.IConfZipService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.iservice.ITenantShippingAddressService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.order.SignalSenderEnum.orderAddress;
import static com.zsmall.common.enums.order.SignalSenderEnum.orderItem;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/3 09:35
 */
@Slf4j
@Lazy
@Component("tiktokOperationV3Handler")
public class TikTokOrderOperationV3Handler extends AbstractOrderBusinessHandler<OrderReceiveFromThirdDTO> {

    @Resource
    private PriceBussinessV2Support priceBussinessV2Support;
    @Resource
    private IProductMappingService iProductMappingService;
    @Resource
    private SysOssMapper sysOssMapper;
    @Resource
    private BillSupport billSupport;
    @Resource
    private IOrderRefundService iOrderRefundService;
    @Resource
    private WholesaleOrderSupport wholesaleSupports;
    @Resource
    private ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    @Resource
    private IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;

    @Resource
    private IProductSkuService iProductSkuService;

    @Resource
    private ProductSkuStockService productSkuStockService;

    @Resource
    private IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    //    @XxlConf(value = "distribution.shipping.address.id.erp",defaultValue = "1704748687534034946")
    @Value("${distribution.shipping.address.id.erp}")
    public String addressId;
    @Value("${distribution.specify.warehouse.id.hj}")
    public String warehouseSystemCode;
    @Resource
    private ITenantShippingAddressService tenantShippingAddressService;

    @Resource
    private IOrderItemShippingRecordService iOrderItemShippingRecordService;
    @Resource
    private OrderItemService orderItemService;

    @Resource
    private OrderSupport orderSupport;

    @Resource
    private IProductSkuStockService iProductSkuStockService;

    @Resource
    private ISysTenantService sysTenantService;

    @Resource
    private IProductService iProductService;

    @Resource
    private OrderAndItemManager orderAndItemManager;

    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private OrdersService ordersService;

    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;

    @Resource
    private IConfZipService iConfZipService;

    @Resource
    private BusinessParameterService businessParameterService;

    @Autowired
    ApplicationContext applicationContext;

    @Resource
    private IWorldLocationService iWorldLocationService;

    @Resource
    private OrderCodeGenerator orderCodeGenerator;


    @Resource
    private IProductSkuAttachmentService iProductSkuAttachmentService;


    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;


    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;


    @Resource
    private IProductSkuService productSkuService;

    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;

    @Resource
    private PriceSupport priceSupport;

    @Override
    public void priceOperation(OrderReceiveFromThirdDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {
        log.info("订单价格计算开始,{}",mappingEnum);
        if(BusinessTypeMappingEnum.HAVE_MAPPING.equals(mappingEnum)){
            log.info("订单价格计算开始,订单相关map:{}",JSON.toJSONString(businessMap));
            List<Orders> orders = businessMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj).collect(Collectors.toList());

            List<OrderItem> items = businessMap.get(orderItem.name()).stream().map(obj -> (OrderItem) obj)
                                               .collect(Collectors.toList());

            List<OrderAddressInfo> addressInfos = businessMap.get(orderAddress.name()).stream().map(obj -> (OrderAddressInfo) obj).collect(Collectors.toList());

            List<OrderItemPrice> itemPrices = businessMap.get(SignalSenderEnum.orderItemPrice.name()).stream().map(obj -> (OrderItemPrice) obj)
                                                         .collect(Collectors.toList());
            List<OrderItemProductSku> orderItemProductSkus = businessMap.get(SignalSenderEnum.orderItemProductSku.name()).stream().map(obj -> (OrderItemProductSku) obj)
                                                         .collect(Collectors.toList());

            List<OrderLogisticsInfo> logisticsInfos = businessMap.get(SignalSenderEnum.orderLogisticsInfo.name()).stream().map(obj -> (OrderLogisticsInfo) obj).collect(Collectors.toList());

            List<OrderItemTrackingRecord> trackingRecords = businessMap.get(SignalSenderEnum.orderItemTrackingRecord.name()).stream().map(obj -> (OrderItemTrackingRecord) obj).collect(Collectors.toList());
            // 此处的作用的颗粒度是最高的,所以此方法不适用于并发场景,除非是针对list内的remove操作
            businessMap.remove(SignalSenderEnum.order.name());
            businessMap.remove(SignalSenderEnum.orderItem.name());
            businessMap.remove(SignalSenderEnum.orderItemPrice.name());
            businessMap.remove(SignalSenderEnum.orderItemProductSku.name());
            businessMap.remove(SignalSenderEnum.orderLogisticsInfo.name());
            businessMap.remove(SignalSenderEnum.orderItemTrackingRecord.name());

            // 移除 businessMap 中 key为SignalSenderEnum.order.name() SignalSenderEnum.orderItem.name() SignalSenderEnum.orderItemPrice.name()的value
            Map<String, OrderItem> itemsMap = items.stream()
                                                   .collect(Collectors.toMap(OrderItem::getOrderNo, Function.identity(), (existing, replacement) -> existing));


            Map<String, OrderAddressInfo> addressInfosMap = addressInfos.stream()
                                                                        .collect(Collectors.toMap(OrderAddressInfo::getOrderNo, Function.identity(), (existing, replacement) -> existing));
            Map<String, OrderItemPrice> itemPricesMap = itemPrices.stream()
                                                                  .collect(Collectors.toMap(OrderItemPrice::getOrderItemNo, Function.identity(), (existing, replacement) -> existing));

            Map<String, OrderItemProductSku> itemProductSkuMap = orderItemProductSkus.stream()
                                                                  .collect(Collectors.toMap(OrderItemProductSku::getOrderNo, Function.identity(), (existing, replacement) -> existing));
            Map<String, OrderLogisticsInfo> logisticsInfoMap = logisticsInfos.stream()
                                                                                     .collect(Collectors.toMap(OrderLogisticsInfo::getOrderNo, Function.identity(), (existing, replacement) -> existing));

            Map<String, OrderItemTrackingRecord> recordMap = trackingRecords.stream()
                                                                          .collect(Collectors.toMap(OrderItemTrackingRecord::getOrderNo, Function.identity(), (existing, replacement) -> existing));
            // 1. 部分订单是需要更新的 2.部分订单是无需修改的
            for (Orders order : orders) {
                // item 判断是否有变化
                OrderItem item = itemsMap.get(order.getOrderNo());
                OrderItemProductSku orderItemProductSku = itemProductSkuMap.get(order.getOrderNo());
                OrderLogisticsInfo orderLogisticsInfo = logisticsInfoMap.get(order.getOrderNo());
                OrderItemTrackingRecord trackingRecord = recordMap.get(order.getOrderNo());
                OrderItemPrice itemPrice = itemPricesMap.get(item.getOrderItemNo());
//                log.info("主订单修改前数据"+JSONObject.toJSONString(order));
//                log.info("子订单修改前数据"+JSONObject.toJSONString(item));
//                log.info("子订单价格修改前数据"+JSONObject.toJSONString(item));
                // 所有订单都要进入校验,重新测算

                OrderPriceCalculateDTO calculateDTO = new OrderPriceCalculateDTO();
                calculateDTO.setOrderItem(item);
                calculateDTO.setLogisticsType(order.getLogisticsType());
                HashMap<String, List<String>> stashMap = orderSupport.getStashList(Collections.singletonList(item));

                String zipCode = addressInfosMap.get(order.getOrderNo()).getZipCode();

                List<String> stashList = new ArrayList<>();
                if(ObjectUtil.isNotEmpty(stashMap)){
                    // 自提也可以查,不会影响自提的价格逻辑,但是可能会影响订单的状态,
                    stashList = stashMap.get(item.getOrderItemNo());
                }
                // 根据 业务类型是否有映射
                priceBussinessV2Support.getTiktokBusinessMap(calculateDTO,order.getTenantId(),zipCode,stashList,item,order,businessMap, orderItemProductSku, orderLogisticsInfo, trackingRecord);
            }
        }

    }

    @Override
    public void thirdToAddressAndLogisticsInfoAndTracking(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                                                          ConcurrentHashMap<String, List<Object>> businessMap,
                                                          ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                                          ChannelTypeEnum channelTypeEnum,
                                                          BusinessTypeMappingEnum mappingEnum) {

        ConcurrentHashMap<String, String> nos = businessNos.get(orderReceiveFromThirdDTO.getLineOrderItemId());
        String erpSku = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0).getErpSku();
        String orderNo = nos.get(SignalSenderEnum.order.name());
        String orderItemNo = nos.get(orderItem.name());
        LogisticsTypeEnum logisticsTypeEnum = LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType());
        String productSkuCode = erpSku;
        Integer totalQuantity = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0).getQuantity();


        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();


        SaleOrderDetailDTO details = orderReceiveFromThirdDTO.getSaleOrderDetails();

//        List<String> result = Arrays.asList(addressDTO.getPostalCode().split("-"));
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        SaleOrderItemDTO dto = saleOrderItemsList.get(0);

        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
        String regionCode = address.getRegionCode();
        String finalZipCode = null;
        // 虽然此处遍历,但外层已处理过基本上是一个sku
        // 拿默认地址模版
        List<TikTokDistrictInfo> districtInfo = address.getDistrictInfo();
        String country = null;
        String state = null;
        String federalDistrict = null;
        String county = null;
        String city = null;

        for (TikTokDistrictInfo tikTokDistrictInfo : districtInfo) {

            String levelName = tikTokDistrictInfo.getAddressLevelName();
            String addressName = tikTokDistrictInfo.getAddressName();
            if ("country".equalsIgnoreCase(levelName)) {
                country = addressName;
            }
            if ("state".equalsIgnoreCase(levelName)) {
                state = addressName;
            }
            if ("Federal District".equalsIgnoreCase(levelName)) {
                federalDistrict = addressName;
            }
            if ("county".equalsIgnoreCase(levelName)) {
                county = addressName;
            }
            if ("city".equalsIgnoreCase(levelName)) {
                city = addressName;
            }
        }


        ConfZip confZip;
        confZip = iConfZipService.getStateCodeByStateName(state);
        if(ObjectUtil.isEmpty(confZip)&&ObjectUtil.isNotEmpty(federalDistrict)){
            confZip = iConfZipService.getStateCodeByStateName(federalDistrict);
        }
        if (ObjectUtil.isEmpty(confZip)) {
            confZip = iConfZipService.getStateCodeByCity(city);
        }

        OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
        orderAddressInfo.setCounty(county);
//           todo 从发号器里拿
        orderAddressInfo.setOrderNo(orderNo);
        orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
        // 拿默认模版里面
        orderAddressInfo.setRecipient(address.getName());

        orderAddressInfo.setPhoneNumber(address.getPhoneNumber());
        // 这三个信息需要调用包裹接口拿到详细的包裹信息
        orderAddressInfo.setCountry(country);
        orderAddressInfo.setCountryCode(regionCode);
        String zipCode = null;
        if (ObjectUtil.isNotEmpty(state)) {
            zipCode = iConfZipService.transZip(address.getPostalCode(), state, county, city);
            orderAddressInfo.setState(state);
        }
        if (ObjectUtil.isNotEmpty(federalDistrict)) {
            zipCode = iConfZipService.transZip(address.getPostalCode(), federalDistrict, county, city);
            orderAddressInfo.setState(federalDistrict);
        }
        finalZipCode = zipCode;
        orderAddressInfo.setStateCode(confZip.getStateCode());

        orderAddressInfo.setCity(city);
        orderAddressInfo.setAddress1(address.getAddressLine1());
        orderAddressInfo.setAddress2(address.getAddressLine2());

        orderAddressInfo.setZipCode(zipCode);
        orderAddressInfo.setEmail(address.getBuyerEmail());
        orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);


        //todo 后续补充
//            orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orderNo);
        orderLogisticsInfo.setShippingLabelExist(true);
        orderLogisticsInfo.setLogisticsZipCode(zipCode);
        orderLogisticsInfo.setLogisticsType(logisticsTypeEnum);
        orderLogisticsInfo.setLogisticsCompanyName(details.getCarrier());
        orderLogisticsInfo.setLogisticsCountryCode(address.getRegionCode());
        orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
        orderLogisticsInfo.setZipCode(zipCode);


        // 物流信息

        List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
        String trimTracking = StrUtil.trim(details.getLogisticsTrackingNo());
        OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
        if (StrUtil.isNotBlank(trimTracking)) {

            trackingRecord.setSku(dto.getErpSku());

            trackingRecord.setLogisticsCarrier(details.getCarrier());
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orderNo);
            trackingRecord.setOrderItemNo(orderItemNo);
            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 用的bizArk仓库ID 此处轮询处理  item.getTotalQuantity()
            // 通过product_sku_code查询仓库product_sku_stock logistics_warehouse_relation warehouse 并且可用库存要大于实际需要扣减的库存
            // tag lty 库存判断
            if(ObjectUtil.isNotEmpty(productSkuCode)){
                trackingRecord.setProductSkuCode(productSkuCode);
                // 此处不能随机了,要找有库存的stocks 然后提供给erp,拿到对应的仓库编号
//                productSkuCode 拿到对应的供应商编号
                ProductSku productSku = productSkuService.queryByProductSkuCode(productSkuCode);
                ProductSkuStock stock = productSkuStockService.getStockForDeliver(regionCode,productSkuCode,LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()),totalQuantity,Boolean.FALSE, finalZipCode, details.getCarrier(),orderReceiveFromThirdDTO.getTenantId(), productSku.getTenantId() );
                if(ObjectUtil.isNotEmpty(stock)){
                    trackingRecord.setWarehouseSystemCode(stock.getWarehouseSystemCode());
                }else{
                    trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
                }
            }

            // tag lty 仓库
            trackingRecord.setQuantity(totalQuantity);

            trackingList.add(trackingRecord);
        }else{

            trackingRecord.setSku(dto.getErpSku());

            if (ObjectUtil.isNotEmpty(productSkuCode)){
                trackingRecord.setProductSkuCode(productSkuCode);
                ProductSku productSku = productSkuService.queryByProductSkuCode(productSkuCode);
                ProductSkuStock stock = productSkuStockService.getStockForDeliver(regionCode,productSkuCode,LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()),totalQuantity,Boolean.FALSE, finalZipCode, details.getCarrier(),orderReceiveFromThirdDTO.getTenantId() , productSku.getTenantId());
                // 如果找不到对应的合适的仓库,则使用默认的仓库
                if(ObjectUtil.isNotEmpty(stock)){
                    trackingRecord.setWarehouseSystemCode(stock.getWarehouseSystemCode());
                }else{
                    trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
                }
            }
            trackingRecord.setLogisticsCarrier(details.getCarrier());
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orderNo);
            trackingRecord.setOrderItemNo(orderItemNo);
            // 线上自提需要放开
            if(ChannelTypeEnum.TikTok.equals(channelTypeEnum)&&LogisticsTypeEnum.PickUp.equals(logisticsTypeEnum)){
                SysOss sysOss = sysOssMapper.selectOne(new LambdaQueryWrapper<SysOss>().eq(SysOss::getBusinessId, orderReceiveFromThirdDTO.getLineOrderItemId()));
                if(ObjectUtil.isNotEmpty(sysOss)){
                    trackingRecord.setLogisticsTrackingNo(sysOss.getBusinessNumber());
                }else{
                    log.error("面单信息获取失败");
                }

            }

            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 仓库改造后选用模版配置后的最近距离的仓库或随机有库存的仓库


            trackingRecord.setQuantity(totalQuantity);
        }
        if(ObjectUtil.isNotEmpty(trackingRecord.getProductSkuCode())){
            businessMap.computeIfAbsent(SignalSenderEnum.orderItemTrackingRecord.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                       .add(trackingRecord);
        }else {
            businessMap.computeIfAbsent(SignalSenderEnum.orderItemTrackingRecord.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                       .add(null);
        }

        businessMap.computeIfAbsent(orderAddress.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderAddressInfo);
        businessMap.computeIfAbsent(SignalSenderEnum.orderLogisticsInfo.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderLogisticsInfo);
    }

    @Override
    public void thirdToOrderItemPrice(OrderReceiveFromThirdDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {
        SaleOrderItemDTO itemDTO = i.getSaleOrderItemsList().get(0);
        ConcurrentHashMap<String, String> nos = businessNos.get(i.getLineOrderItemId());
        String erpSku = itemDTO.getErpSku();
        String orderNo = nos.get(SignalSenderEnum.order.name());
        String orderItemNo = nos.get(orderItem.name());

        OrderItemPrice itemPrice = new OrderItemPrice();
        if(ObjectUtil.isNotEmpty(erpSku)){
            OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
            // 订单明细价格
            OrderItem orderItem = new OrderItem();
            orderItem.setProductSkuCode(erpSku);
            orderItem.setOrderItemNo(orderItemNo);
            orderItem.setOrderNo(orderNo);
            orderItem.setTotalQuantity(itemDTO.getQuantity());
            orderItem.setChannelType(channelTypeEnum);

            itemPrice.setOrderItemNo(orderItemNo);
            itemPrice.setProductSkuCode(erpSku);
            itemPrice.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(i.getLogisticsType()));
            itemPrice.setTotalQuantity(itemDTO.getQuantity());

        }
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(itemPrice);

    }

    @Override
    public void thirdToOrderItemProductSku(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ConcurrentHashMap<String, List<Object>> businessMap,
                                           ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                           ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {
        OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
        String orderItemNo = businessNos.get(orderReceiveFromThirdDTO.getLineOrderItemId()).get(SignalSenderEnum.orderItem.name());
        String orderNo = businessNos.get(orderReceiveFromThirdDTO.getLineOrderItemId()).get(SignalSenderEnum.order.name());
        orderItemProductSku.setOrderNo(orderNo);
        orderItemProductSku.setOrderItemNo(orderItemNo);

        // 通过 自订单编号进行关联,
        orderItemProductSkuThirdService.setBusinessField(orderItemProductSku, orderReceiveFromThirdDTO, channelTypeEnum);
        if(ObjectUtil.isNotEmpty(orderItemProductSku.getProductSkuCode())){
            businessMap.computeIfAbsent(SignalSenderEnum.orderItemProductSku.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                       .add(orderItemProductSku);
        }else{
            businessMap.computeIfAbsent(SignalSenderEnum.orderItemProductSku.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                       .add(null);
        }


    }

    @Override
    public void thirdToOrderItem(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ConcurrentHashMap<String, List<Object>> businessMap,
                                 ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                 ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        String orderNo = businessNos.get(orderReceiveFromThirdDTO.getLineOrderItemId()).get(SignalSenderEnum.order.name());
        String orderItemNo = businessNos.get(orderReceiveFromThirdDTO.getLineOrderItemId()).get(orderItem.name());
        SaleOrderItemDTO itemDTO = saleOrderItemsList.get(0);
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderItemNo(orderItemNo);
        orderItem.setOrderNo(orderNo);
        orderItem.setChannelSku(itemDTO.getSkuId());
        orderItem.setChannelType(channelTypeEnum);
        // 核对金额
        orderItemService.setSiteField(orderItem, orderReceiveFromThirdDTO);
        orderItemService.setOrderBusinessField(orderItem, orderReceiveFromThirdDTO, channelTypeEnum, itemDTO);
        orderItemService.setChannelTag(orderItem, orderReceiveFromThirdDTO, channelTypeEnum, itemDTO);
        iOrderItemThirdService.setOrderTagSystem(orderItem, orderReceiveFromThirdDTO, channelTypeEnum, itemDTO);


        log.info("订单项信息:{}", JSONUtil.toJsonStr(orderItem));
        businessMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderItem);

    }

    @Override
    public void thirdToOrder(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ConcurrentHashMap<String, List<Object>> businessMap,
                             ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                             ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {
        Orders orders = new Orders();
        String orderNo = businessNos.get(orderReceiveFromThirdDTO.getLineOrderItemId()).get(SignalSenderEnum.order.name());
        orders.setOrderNo(orderNo);
        orders.setOrderExtendId(orderNo);
        orders.setChannelType(channelTypeEnum);

        orders = ordersService.setSiteField(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setOrderBusinessField(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setOrderTagSystem(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setChannelTag(orderReceiveFromThirdDTO, orders);
        if(BusinessTypeMappingEnum.NO_MAPPING.equals(mappingEnum)){
            orders.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
            JSONObject errorMsg = ordersService.getAbnormalServiceMessage(orders, orderReceiveFromThirdDTO);
            orders.setPayErrorMessage(errorMsg);
        }
        if(BusinessTypeMappingEnum.HAVE_MAPPING.equals(mappingEnum)){
            orders.setExceptionCode(OrderExceptionEnum.normal.getValue());
        }
        // 使用computeIfAbsent来确保键存在且其值被正确初始化  价格存在问题
        businessMap.computeIfAbsent(SignalSenderEnum.order.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orders);

    }

    /**
     * 功能描述：业务批量保存,关于尾程异常的问题,价格的变更已在前面处理,这里只需要保存即可
     * tag lty save
     * @param businessMap     商业地图
     * @param channelTypeEnum 通道类型枚举
     * <AUTHOR>
     * @date 2024/08/06
     */
    @Override
    public void businessBatchSave(ConcurrentHashMap<String, List<Object>> businessMap,
                                  ChannelTypeEnum channelTypeEnum) {

        List<Orders> orders = businessMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj)
            .collect(Collectors.toList());
        log.info("过滤后准备进行保存的订单项:{}", JSON.toJSONString(orders));
        List<OrderItem> items = businessMap.get(orderItem.name()).stream().map(obj -> (OrderItem) obj)
                                             .collect(Collectors.toList());
        List<OrderItemPrice> itemPrices = businessMap.get(SignalSenderEnum.orderItemPrice.name()).stream().map(obj -> (OrderItemPrice) obj)
                                                    .collect(Collectors.toList());
        List<OrderItemProductSku> itemProductSkus = businessMap.get(SignalSenderEnum.orderItemProductSku.name()).stream().map(obj -> (OrderItemProductSku) obj)
                                                          .collect(Collectors.toList());
        List<OrderItemTrackingRecord> trackingRecords = businessMap.get(SignalSenderEnum.orderItemTrackingRecord.name()).stream().map(obj -> (OrderItemTrackingRecord) obj)
                                                               .collect(Collectors.toList());
        List<OrderLogisticsInfo> logisticsInfos = businessMap.get(SignalSenderEnum.orderLogisticsInfo.name()).stream().map(obj -> (OrderLogisticsInfo) obj).collect(Collectors.toList());
        List<OrderAddressInfo> addressInfos = businessMap.get(orderAddress.name()).stream().map(obj -> (OrderAddressInfo) obj).collect(Collectors.toList());

        iOrdersService.saveBatch(orders);
        log.info("线程:{}--|,实际录入记录总数:{}",Thread.currentThread().getId(),orders.size());
        // orders 转换成map key是orderNo value是 id
        Map<String, Long> orderMaps = orders.stream()
                                            .collect(Collectors.toMap(Orders::getOrderNo, Orders::getId));
        items.forEach(item->{
            item.setOrderId(orderMaps.get(item.getOrderNo()));
        });

        iOrderItemService.saveBatch(items);
        Map<String, Long> itemMaps = items.stream().collect(Collectors.toMap(OrderItem::getOrderItemNo, OrderItem::getId));
        boolean haveSku = itemPrices.stream()
                                    .noneMatch(itemPrice -> ObjectUtil.isEmpty(itemPrice.getProductSkuCode()));
        if (CollUtil.isNotEmpty(itemPrices) && itemPrices.stream().noneMatch(Objects::isNull)&&haveSku) {
            // 判断itemPrices 内的元素的productSkuCode为null 返回false
            if (CollUtil.isNotEmpty(itemMaps)) {
                itemPrices.forEach(itemPrice -> {
                    if (itemPrice != null) { // 显式检查itemPrice是否非null
                        itemPrice.setOrderItemId(itemMaps.get(itemPrice.getOrderItemNo()));
                    }
                });
            }
            iOrderItemPriceService.saveBatch(itemPrices);

        }

        if (CollUtil.isNotEmpty(itemProductSkus) && itemProductSkus.stream().noneMatch(Objects::isNull)) {
            if (CollUtil.isNotEmpty(itemMaps)) {
                itemProductSkus.forEach(itemProductSku -> {
                    if (itemProductSku != null) { // 显式检查itemPrice是否非null
                        String orderItemNo = itemProductSku.getOrderItemNo();
                        Long orderItemId = itemMaps.get(orderItemNo);
                        itemProductSku.setOrderItemId(orderItemId);
                    }
                });
            }
        }

        // 假设 itemProductSkus 是一个 List<YourItemType>，其中 YourItemType 是你的产品类型
        if (CollUtil.isNotEmpty(itemProductSkus)) {
            if (CollUtil.isNotEmpty(itemMaps)) {
                // 使用 Stream API 过滤掉 null 元素
                List<OrderItemProductSku> nonNullItemProductSkus = itemProductSkus.stream()
                                                                                  .filter(Objects::nonNull) // 过滤掉 null 元素
                                                                                  .peek(itemProductSku -> itemProductSku.setOrderItemId(itemMaps.get(itemProductSku.getOrderItemNo()))) // 在保存之前设置 orderItemId
                                                                                  .collect(Collectors.toList()); // 收集结果到新的 List

                // 如果过滤后还有元素，则保存
                if (!nonNullItemProductSkus.isEmpty()) {
                    iOrderItemProductSkuService.saveBatch(nonNullItemProductSkus);
                }
            }
        }

        // 对 trackingRecords 进行相同的处理（如果它包含 null 元素且 saveBatch 不能处理它们）
        if (CollUtil.isNotEmpty(trackingRecords)) {
            // 使用 Stream API 过滤掉 null 元素
            List<OrderItemTrackingRecord> nonNullTrackingRecords = trackingRecords.stream()
                                                                                 .filter(Objects::nonNull)
                                                                                 .collect(Collectors.toList());

            // 如果过滤后还有元素，则保存
            if (!nonNullTrackingRecords.isEmpty()) {
                iOrderItemTrackingRecordService.saveBatch(nonNullTrackingRecords);
            }
            // 或者，如果即使只有一个非null元素也需要保存，可以直接调用saveBatch
            // iOrderItemTrackingRecordService.saveBatch(nonNullTrackingRecords);
        }


// 对 logisticsInfos 进行处理
        if (CollUtil.isNotEmpty(logisticsInfos)) {
            List<OrderLogisticsInfo> nonNullLogisticsInfos = logisticsInfos.stream()
                                                                           .filter(Objects::nonNull)
                                                                           .peek(item -> item.setOrderId(orderMaps.get(item.getOrderNo())))
                                                                           .collect(Collectors.toList());

            if (!nonNullLogisticsInfos.isEmpty()) {
                iOrderLogisticsInfoService.saveBatch(nonNullLogisticsInfos);
            }
        }

// 对 addressInfos 进行处理
        if (CollUtil.isNotEmpty(addressInfos)) {
            List<OrderAddressInfo> nonNullAddressInfos = addressInfos.stream()
                                                                     .filter(Objects::nonNull)
                                                                     .peek(item -> item.setOrderId(orderMaps.get(item.getOrderNo())))
                                                                     .collect(Collectors.toList());

            if (!nonNullAddressInfos.isEmpty()) {
                iOrderAddressInfoService.saveBatch(nonNullAddressInfos);
            }
        }
        log.info("tiktok数据插入完成,{}",JSON.toJSONString(orders));
    }
}
