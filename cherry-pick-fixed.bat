@echo off
setlocal enabledelayedexpansion
title Git Cherry-pick Tool (Fixed)

echo ===== Git Cherry-pick Tool (Fixed Version) =====
echo.

echo Checking Git environment...
git --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Git not found!
    pause
    exit /b 1
)

echo Checking Git repository...
git status >nul 2>&1
if errorlevel 1 (
    echo ERROR: Not a Git repository!
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo Checking source branch...
git show-ref --verify --quiet refs/heads/Len_product_Lock_0711
if errorlevel 1 (
    echo ERROR: Branch Len_product_Lock_0711 not found!
    echo Available branches:
    git branch
    pause
    exit /b 1
)

echo.
echo Recent commits from Len_product_Lock_0711:
echo ==========================================
git log --oneline Len_product_Lock_0711 -10
echo ==========================================
echo.

:input_hash
set /p commit_hash="Enter commit hash to cherry-pick: "

if "%commit_hash%"=="" (
    echo ERROR: Hash cannot be empty!
    goto input_hash
)

echo.
echo Selected hash: %commit_hash%

git cat-file -e %commit_hash% >nul 2>&1
if errorlevel 1 (
    echo ERROR: Hash %commit_hash% not found!
    goto input_hash
)

echo Hash validated successfully.
echo.

echo Checking current working directory status...
git status --porcelain >nul 2>&1
if not errorlevel 1 (
    for /f %%i in ('git status --porcelain ^| find /c /v ""') do set change_count=%%i
    if !change_count! gtr 0 (
        echo WARNING: You have !change_count! uncommitted changes.
        echo These will be automatically stashed during operations.
        echo Current changes:
        git status --short
        echo.
    )
)

set /p confirm="Apply to activity_by_pre_20250702_v1.0 and stage? (y/n): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting cherry-pick operations...
echo.

REM Remember original branch
for /f "tokens=*" %%i in ('git branch --show-current') do set original_branch=%%i
echo Original branch: %original_branch%
echo.

echo === Step 1: activity_by_pre_20250702_v1.0 ===
git checkout activity_by_pre_20250702_v1.0
if errorlevel 1 (
    echo ERROR: Cannot switch to activity_by_pre_20250702_v1.0
    pause
    exit /b 1
)

echo Checking for local changes...
git status --porcelain >nul 2>&1
if not errorlevel 1 (
    for /f %%i in ('git status --porcelain ^| find /c /v ""') do set change_count=%%i
    if !change_count! gtr 0 (
        echo WARNING: Local changes detected. Stashing them...
        git stash push -m "Auto-stash before cherry-pick %commit_hash%"
        if errorlevel 1 (
            echo ERROR: Failed to stash changes
            pause
            exit /b 1
        )
        echo Local changes stashed successfully.
    )
)

echo Updating branch...
git pull origin activity_by_pre_20250702_v1.0

echo Executing cherry-pick...
git cherry-pick %commit_hash%
if errorlevel 1 (
    echo.
    echo CONFLICT detected in activity_by_pre_20250702_v1.0!
    echo Please resolve conflicts manually:
    echo   1. Fix conflicts in files
    echo   2. Run: git add ^<resolved-files^>
    echo   3. Run: git cherry-pick --continue
    echo   4. Run: git push origin activity_by_pre_20250702_v1.0
    echo.
    echo Or abort with: git cherry-pick --abort
    echo.
    echo After resolving, you can continue with stage branch manually.
    pause
    exit /b 1
)

echo Pushing changes...
git push origin activity_by_pre_20250702_v1.0
echo [OK] activity_by_pre_20250702_v1.0 completed
echo.

echo === Step 2: stage ===
git checkout stage
if errorlevel 1 (
    echo ERROR: Cannot switch to stage
    pause
    exit /b 1
)

echo Checking for local changes...
git status --porcelain >nul 2>&1
if not errorlevel 1 (
    for /f %%i in ('git status --porcelain ^| find /c /v ""') do set change_count=%%i
    if !change_count! gtr 0 (
        echo WARNING: Local changes detected. Stashing them...
        git stash push -m "Auto-stash before cherry-pick %commit_hash%"
        if errorlevel 1 (
            echo ERROR: Failed to stash changes
            pause
            exit /b 1
        )
        echo Local changes stashed successfully.
    )
)

echo Updating branch...
git pull origin stage

echo Executing cherry-pick...
git cherry-pick %commit_hash%
if errorlevel 1 (
    echo.
    echo CONFLICT detected in stage!
    echo Please resolve conflicts manually:
    echo   1. Fix conflicts in files
    echo   2. Run: git add ^<resolved-files^>
    echo   3. Run: git cherry-pick --continue
    echo   4. Run: git push origin stage
    echo.
    echo Or abort with: git cherry-pick --abort
    pause
    exit /b 1
)

echo Pushing changes...
git push origin stage
echo [OK] stage completed
echo.

echo === Results ===
echo Commit %commit_hash% applied to:
echo   [OK] activity_by_pre_20250702_v1.0
echo   [OK] stage
echo.

echo Verification:
echo.
echo activity_by_pre_20250702_v1.0 latest:
git checkout activity_by_pre_20250702_v1.0
git log --oneline -3
echo.
echo stage latest:
git checkout stage
git log --oneline -3
echo.

echo === Switching back to original branch ===
echo Switching back to %original_branch%...
git checkout %original_branch%
if errorlevel 1 (
    echo WARNING: Failed to switch back to %original_branch%
    echo Switching to Len_product_Lock_0711 instead...
    git checkout Len_product_Lock_0711
)

echo Current branch:
git branch --show-current
echo.

echo === Checking for stashed changes ===
git stash list | findstr "Auto-stash before cherry-pick %commit_hash%" >nul
if not errorlevel 1 (
    echo Found auto-stashed changes from this operation.
    set /p restore_stash="Restore stashed changes? (y/n): "
    if /i "!restore_stash!"=="y" (
        echo Restoring stashed changes...
        git stash pop
        if not errorlevel 1 (
            echo [OK] Stashed changes restored successfully.
        ) else (
            echo WARNING: Failed to restore stashed changes.
            echo You can manually restore with: git stash pop
        )
    )
)

echo.
echo SUCCESS: All operations completed!
echo.
pause
